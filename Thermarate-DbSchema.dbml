// - User - //

Table RSS_User {
  UserId               UUID         [not null, PK]
  AspNetUserId         varchar(128) [null, ref: > AspNetUsers.Id, name: 'FK_RSS_User_AspNetUsers']
  FullName             varchar(120) [not null]
  FirstName            varchar(60)  [not null]
  LastName             varchar(60)  [not null]
  EmailAddress         varchar(150) [null]
  Phone                varchar(20)  [null]
  MobilePhone          varchar(20)  [null]
  BusinessUnitId       int          [null, ref: > RSS_BusinessUnit.BusinessUnitId, name: 'FK_RSS_User_RSS_BusinessUnit']
  JobTitle             varchar(200) [null]
  BusinessRoleCode     varchar(20)  [null, ref: > RSS_BusinessRole.BusinessRoleCode, name: 'FK_RSS_BusinessRole_RSS_User']
  SignatureSVGImage    varchar      [null]
  Deleted              boolean      [not null, default: FALSE]
  CreatedOn            timestamp    [not null]
  CreatedByName        varchar(50)  [not null]
  ModifiedOn           timestamp    [null]
  ModifiedByName       varchar(50)  [null]
  ClientId             UUID         [null, ref: > RSS_Client.ClientId, name: 'FK_RSS_User_RSS_Client_ClientId']
  IsExternal           int          [not null]
  SignatureBase64Image varchar      [null]
  IsGoogleLinked       boolean      [not null, default: FALSE]
  IsMicrosoftLinked    boolean      [not null, default: FALSE]
}


Table AspNetUsers {
  Id                                      varchar(128) [not null, PK]
  UserName                                varchar      [null]
  PasswordHash                            varchar      [null]
  SecurityStamp                           varchar      [null]
  EmailConfirmed                          boolean      [not null]
  PhoneNumber                             varchar      [null]
  PhoneNumberConfirmed                    boolean      [not null]
  TwoFactorEnabled                        boolean      [not null]
  LockoutEndDateUtc                       timestamp    [null]
  LockoutEnabled                          boolean      [not null]
  AccessFailedCount                       int          [not null]
  ApplicationId                           UUID         [not null]
  LegacyPasswordHash                      varchar      [null]
  LoweredUserName                         varchar(256) [not null]
  MobileAlias                             varchar(16)  [null]
  IsAnonymous                             boolean      [not null]
  LastActivityDate                        timestamp    [not null]
  MobilePIN                               varchar(16)  [null]
  Email                                   varchar(256) [null]
  LoweredEmail                            varchar(256) [null]
  PasswordQuestion                        varchar(256) [null]
  PasswordAnswer                          varchar(128) [null]
  IsApproved                              boolean      [not null]
  IsLockedOut                             boolean      [not null]
  CreateDate                              timestamp    [not null]
  LastLoginDate                           timestamp    [not null]
  LastPasswordChangedDate                 timestamp    [not null]
  LastLockoutDate                         timestamp    [not null]
  FailedPasswordAttemptCount              int          [not null]
  FailedPasswordAttemptWindowStart        timestamp    [not null]
  FailedPasswordAnswerAttemptCount        int          [not null]
  FailedPasswordAnswerAttemptWindowStart  timestamp    [not null]
  Comment                                 varchar      [null]
  LastTwoFacAuthDate                      timestamp    [null]
  TwoFacAuthSecret                        varbinary(128) [null]
}


Table AspNetRoles {
  Id            varchar(128) [not null, PK]
  Name          varchar      [not null]
  Discriminator varchar(128) [null]
  Description   varchar      [null]
  SortOrder     int          [not null]
  ParentRoleId  UUID         [null]
  Type          varchar(50)  [not null]
}


Table AspNetUserClaims {
  Id         int          [not null, PK]
  UserId     varchar(128) [not null, ref: > AspNetUsers.Id, name: 'FK_dbo_AspNetUserClaims_dbo_AspNetUsers_UserId']
  ClaimType  varchar      [null]
  ClaimValue varchar      [null]
}


Table AspNetUserLogins {
  LoginProvider varchar(128) [not null, PK]
  ProviderKey   varchar(128) [not null, PK]
  UserId        varchar(128) [not null, ref: > AspNetUsers.Id, name: 'FK_dbo_AspNetUserLogins_dbo_AspNetUsers_UserId']
}


Table AspNetUserRoles {
  UserId varchar(128) [not null, PK, ref: > AspNetUsers.Id, name: 'FK_dbo_AspNetUserRoles_dbo_AspNetUsers_UserId']
  RoleId varchar(128) [not null, PK, ref: > AspNetRoles.Id, name: 'FK_dbo_AspNetUserRoles_dbo_AspNetRoles_RoleId']
}


// - Business Unit and Role Tables - //

Table RSS_BusinessUnit {
  BusinessUnitId     int          [not null, PK]
  Name               varchar(100) [not null]
  BusinessUnitTypeId int          [null, ref: > RSS_BusinessUnitType.BusinessUnitTypeId, name: 'FK_RSS_BusinessUnit_RSS_BusinessUnitType']
  StateCode          varchar(20)  [null, ref: > RSS_State.StateCode, name: 'FK_RSS_BusinessUnit_RSS_State']
  ManagerUserId      UUID         [null, ref: > RSS_User.UserId, name: 'FK_RSS_BusinessUnit_RSS_User']
  ParentBusinessUnitId int        [null, ref: > RSS_BusinessUnit.BusinessUnitId, name: 'FK_RSS_BusinessUnit_RSS_User_Ref']
  Deleted            boolean      [not null, default: FALSE]
}


Table RSS_BusinessUnitType {
  BusinessUnitTypeId int          [not null, PK]
  Description        varchar(100) [not null]
  Deleted            boolean      [not null, default: FALSE]
}


Table RSS_BusinessRole {
  BusinessRoleCode varchar(20)  [not null, PK]
  Description      varchar(100) [not null]
  Deleted          boolean      [not null, default: FALSE]
}


Table RSS_EmployeeRole {
  EmployeeRoleId   int          [not null, PK]
  Description      varchar(100) [not null]
  Deleted          boolean      [not null, default: FALSE]
}


Table RSS_MapToUserRoles {
  MapToUserRoleId  int          [not null, PK]
  EmployeeRoleId   int          [not null, ref: > RSS_EmployeeRole.EmployeeRoleId, name: 'FK_RSS_MapToUserRoles_RSS_EmployeeRole']
  AspNetRoleId     varchar(128) [not null]
  Deleted          boolean      [not null, default: FALSE]
}


Table RSS_RolesBusinessRole {
  RoleBusinessRoleId int          [not null, PK]
  AspNetRoleId       varchar(128) [not null, ref: > AspNetRoles.Id, name: 'FK_RSS_RolesBusinessRole_AspNetRoles']
  BusinessRoleCode   varchar(20)  [not null]
  Deleted            boolean      [not null, default: FALSE]
}


Table RSS_RolesUserSelectionsData {
  RoleUserSelectionId int          [not null, PK]
  AspNetRoleId        varchar(128) [not null, ref: > AspNetRoles.Id, name: 'FK_RSS_RolesUserSelectionsData_AspNetRoles']
  AspNetUserId        varchar(128) [not null, ref: > AspNetUsers.Id, name: 'FK_RSS_RolesUserSelectionsData_AspNetUsers']
  Deleted             boolean      [not null, default: FALSE]
}


// - Job - //

Table RSS_Job {
  JobId               UUID         [not null, PK]
  ClientId            UUID         [not null, ref: > RSS_Client.ClientId, name: 'FK_RSS_Job_RSS_Client']
  JobReference        varchar(40)  [null]
  StatusCode          varchar(20)  [not null, ref: > RSS_Status.StatusCode, name: 'FK_RSS_Job_RSS_Status']
  Notes               varchar      [null]
  CreatedOn           timestamp    [not null]
  CreatedByName       varchar(50)  [null]
  ModifiedOn          timestamp    [null]
  ModifiedByName      varchar(50)  [null]
  Deleted             boolean      [not null]
  CurrentAssessmentId UUID         [null, ref: > RSS_Assessment.AssessmentId, name: 'FK_RSS_Job_RSS_Assessment_CurrentAssessmentId']
  AssessorUserId      UUID         [null, ref: > RSS_User.UserId, name: 'FK_RSS_Job_RSS_User_AssessorUserId']
}
// INDEXES
//      - IX_RSS_Job_CreatedOn (CreatedOn, Deleted)
//      - IX_RSS_Job_ClientId (ClientId, Deleted)
//      - IX_RSS_Job_StatusCode (StatusCode, Deleted)
//      - IX_RSS_Job_UserId (AssessorUserId)
//      - IX_RSS_Job_ModifiedOn (ModifiedOn, Deleted, StatusCode)


Table RSS_JobEvent {
  JobEventId    UUID         [not null, PK]
  JobId         UUID         [not null, ref: > RSS_Job.JobId, name: 'FK_RSS_JobEvent_RSS_Job']
  EventTypeCode varchar(20)  [not null, ref: > RSS_EventType.EventTypeCode, name: 'FK_RSS_JobEvent_RSS_EventType']
  EventDate     timestamp    [not null]
  Description   varchar(500) [null]
  CreatedOn     timestamp    [not null]
  CreatedByName varchar(50)  [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_EventType {
  EventTypeCode varchar(20)  [not null, PK]
  Description   varchar(100) [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_NotificationRule {
  NotificationRuleId   UUID         [not null, PK]
  InitialStatusCode    varchar(20)  [not null, ref: > RSS_Status.StatusCode, name: 'FK_RSS_RSS_NotificationRule_InitialStatusCode']
  NewStatusCode        varchar(20)  [not null, ref: > RSS_Status.StatusCode, name: 'FK_RSS_RSS_NotificationRule_NewStatusCode']
  Description          varchar(200) [null]
  Deleted              boolean      [not null, default: FALSE]
}


Table RSS_NotificationRuleRecipientType {
  NotificationRuleRecipientTypeId UUID         [not null, PK]
  NotificationRuleId              UUID         [not null, ref: > RSS_NotificationRule.NotificationRuleId, name: 'FK_RSS_NotificationRuleRecipientType_NotificationRuleId']
  RecipientCode                   varchar(20)  [not null, ref: > RSS_RecipientCode.RecipientCode, name: 'FK_RSS_NotificationRuleRecipientType_RecipientCode']
  Deleted                         boolean      [not null, default: FALSE]
}


Table RSS_RecipientCode {
  RecipientCode varchar(20)  [not null, PK]
  Description   varchar(100) [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_NotificationDispatchCheckRequest {
  NotificationDispatchCheckRequestId UUID         [not null, PK]
  AssessmentId                       UUID         [not null, ref: > RSS_Assessment.AssessmentId, name: 'FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId']
  InitialStatusCode                  varchar(20)  [not null, ref: > RSS_Status.StatusCode, name: 'FK_RSS_NotificationDispatchCheckRequest_InitialStatusCode']
  NewStatusCode                      varchar(20)  [not null, ref: > RSS_Status.StatusCode, name: 'FK_RSS_NotificationDispatchCheckRequest_NewStatusCode']
  RequestDate                        timestamp    [not null]
  Processed                          boolean      [not null, default: FALSE]
  Deleted                            boolean      [not null, default: FALSE]
}


// - Client - //

Table RSS_Client {
  ClientId                      UUID         [not null, PK]
  ClientName                    varchar(100) [not null]
  CreatedOn                     timestamp    [not null, default: 'now()']
  CreatedByName                 varchar(50)  [null]
  ModifiedOn                    timestamp    [null]
  ModifiedByName                varchar(50)  [null]
  Deleted                       boolean      [not null, default: FALSE]
  AccountsFirstName             varchar(100) [null]
  AccountsLastName              varchar(100) [null]
  AccountsPhone                 varchar(20)  [null]
  AccountsEmail                 varchar(150) [null]
  AccountsNote                  varchar(500) [null]
  AccountsSameAsContact         boolean      [null]
  ClientDefaultId               UUID         [null, ref: > RSS_ClientDefault.ClientDefaultId, name: 'FK_RSS_Client_RSS_ClientDefault']
  IsFavourite                   boolean      [null]
  DefaultClientAssigneeUserId   UUID         [null, ref: > RSS_User.UserId, name: 'FK_RSS_Client_RSS_User_UserId']
  ClientOptionsJson             varchar      [null]
  ReportSettingsJson            varchar      [null]
  ClientCostItemsJson           varchar      [null]
  EnergyLabsSettingsJson        varchar      [null]
}


Table RSS_UsersClient {
  UserClientId   UUID         [not null, PK]
  UserId         varchar(128) [not null, ref: > AspNetUsers.Id, name: 'FK_RSS_UsersClient_AspNetUsers']
  ClientId       UUID         [not null, ref: > RSS_Client.ClientId, name: 'FK_RSS_UsersClient_RSS_Client']
  CreatedOn      timestamp    [not null, default: 'now()']
  CreatedByName  varchar(100) [not null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(100) [null]
  Deleted        boolean      [not null]
}


Table RSS_ClientDefault {
  ClientDefaultId             UUID         [not null, PK]
  ClientId                    UUID         [not null, ref: > RSS_Client.ClientId, name: 'FK_RSS_ClientDefault_RSS_Client']
  ProjectTypeCode             varchar(20)  [null, ref: > RSS_ProjectType.ProjectTypeCode, name: 'FK_RSS_ClientDefault_RSS_ProjectType']
  AssessmentSoftwareCode      varchar(20)  [null, ref: > RSS_AssessmentSoftware.AssessmentSoftwareCode, name: 'FK_RSS_ClientDefault_RSS_AssessmentSoftware']
  BuildingExposureCode        varchar(20)  [null, ref: > RSS_BuildingExposure.BuildingExposureCode, name: 'FK_RSS_ClientDefault_RSS_BuildingExposure']
  ComplianceMethodCode        varchar(20)  [null, ref: > RSS_ComplianceMethod.ComplianceMethodCode, name: 'FK_RSS_ClientDefault_RSS_ComplianceMethod']
  CertificationCode           varchar(20)  [null, ref: > RSS_Certification.CertificationId, name: 'FK_RSS_ClientDefault_RSS_Certification_CertificationId']
  PriorityCode                varchar(20)  [null]
  WorksDescriptionCode        varchar(20)  [null, ref: > WorksDescriptionCode.WorksDescriptionCode, name: 'FK_RSS_ClientDefault_WorksDescriptionCode']
  CreatedOn                   timestamp    [not null, default: 'now()']
  CreatedByName               varchar(50)  [null]
  CreatedByUserId             UUID         [null, ref: > RSS_User.UserId, name: 'FK_RSS_ClientDefault_RSS_User']
  ModifiedOn                  timestamp    [null]
  ModifiedByName              varchar(50)  [null]
  Deleted                     boolean      [not null, default: FALSE]
  SpaceHeatingServiceTypeCode varchar(100) [null]
  SpaceHeatingGems2019Rating  decimal(4,1) [null]
  SpaceCoolingServiceTypeCode varchar(100) [null]
  SpaceCoolingGems2019Rating  decimal(4,1) [null]
  WaterHeatingServiceTypeCode varchar(100) [null]
  WaterHeatingGems2019Rating  decimal(4,1) [null]
  SwimmingPoolExists          boolean      [null]
  SwimmingPoolVolume          decimal(4,1) [null]
  SwimmingPoolGems2019Rating  decimal(4,1) [null]
  SpaExists                   boolean      [null]
  SpaVolume                   decimal(4,1) [null]
  SpaGems2019Rating           decimal(4,1) [null]
  PhotovoltaicExists          boolean      [null]
  PhotovoltaicCapacity        decimal(4,1) [null]
  IncludeDrawingsInReport     boolean      [not null, default: TRUE]
  StampDrawings               boolean      [not null, default: TRUE]
}


// - Project - //

Table RSS_Project {
  ProjectId              UUID          [not null, PK]
  ClientId               UUID          [null, ref: > RSS_Client.ClientId, name: 'FK_RSS_Project_RSS_Client']
  ProjectName            varchar(100)  [not null]
  Description            varchar(500)  [null]
  ProjectTypeCode        varchar(50)   [null, ref: > RSS_ProjectType.ProjectTypeCode, name: 'FK_RSS_Project_RSS_ProjectType']
  IsActive               boolean       [not null, default: TRUE]
  Lots                   int           [null]
  LotArea                decimal(16,6) [null]
  LogoFileId             UUID          [null, ref: > RSS_File.FileId, name: 'FK_RSS_Project_RSS_File_LogoFileId']
  SuburbCode             UUID          [null, ref: > RSS_Suburb.SuburbCode, name: 'FK_RSS_Project_RSS_Suburb']
  StateCode              varchar(20)   [null, ref: > RSS_State.StateCode, name: 'FK_RSS_Project_RSS_State']
  LGA                    varchar(100)  [null]
  LGAShort               varchar(100)  [null]
  NatHERSClimateZoneCode varchar(20)   [null, ref: > RSS_NatHERSClimateZone.NatHERSClimateZoneCode, name: 'FK_RSS_Project_RSS_NatHERSClimateZone']
  NCCClimateZoneCode     varchar(20)   [null, ref: > RSS_NCCClimateZone.NCCClimateZoneCode, name: 'FK_RSS_Project_RSS_NCCClimateZone']
  Latitude               decimal(13,9) [null]
  Longitude              decimal(13,9) [null]
  LockWOHLocation        boolean       [not null, default: FALSE]
  EnergyLabsSettingsJson varchar       [null]
  VariationOptionsJson   varchar       [null, note: 'Stores project variation options as JSON']
  CreatedOn              timestamp     [not null, default: 'now()']
  CreatedByName          varchar(50)   [null, default: 'System']
  ModifiedOn             timestamp     [null]
  ModifiedByName         varchar(50)   [null]
  Deleted                boolean       [not null, default: FALSE]
  SortOrder              int           [not null, default: 999]
}


Table RSS_ProjectType {
  ProjectTypeCode varchar(50)  [not null, PK]
  Description     varchar(200) [not null]
  SortOrder       smallint     [not null, default: 999]
  Deleted         boolean      [not null, default: FALSE]
}


Table RSS_ProjectDescription {
  ProjectDescriptionCode varchar(20)  [not null, PK]
  Description            varchar(100) [null]
  ProjectRatingModeCode  varchar(20)  [null, ref: > RSS_ProjectRatingMode.ProjectRatingModeCode, name: 'FK_ProjectDescription_ProjectRatingMode']
  Deleted                boolean      [not null, default: FALSE]
}


Table BuildingDesignTemplate {
  BuildingDesignTemplateId UUID         [not null, PK]
  ProjectDescriptionCode   varchar(20)  [not null, ref: > RSS_ProjectDescription.ProjectDescriptionCode, name: 'FK_BuildingDesignTemplate_ProjectDescription']
  Name                     varchar(100) [not null]
  Description              varchar(500) [null]
  Deleted                  boolean      [not null, default: FALSE]
}





Table ASSCOMPSIM {
  AssCompSimId UUID [not null, PK]
  SimulationId UUID [not null, ref: > RSS_AssessmentComplianceOption.ComplianceOptionsId, name: 'FK_ASSCOMPSIM_ID']
  Description  varchar(100) [null]
  Deleted      boolean [not null, default: FALSE]
}


Table RSS_ComplianceOption {
  ComplianceOptionId UUID [not null, PK]
  OptionId           UUID [not null, ref: > RSS_AssessmentComplianceOption.ComplianceOptionsId, name: 'FK_RSS_ComplianceOption_ID']
  Description        varchar(100) [null]
  Deleted            boolean [not null, default: FALSE]
}


Table RSS_ProjectRatingMode {
  ProjectRatingModeCode varchar(20)  [not null, PK]
  Description           varchar(100) [null]
  Deleted               boolean      [not null, default: FALSE]
}


Table RSS_UserProject {
  UserProjectId  UUID         [not null, PK]
  UserId         UUID         [not null, ref: > RSS_User.UserId, name: 'FK_RSS_UserProject_RSS_User']
  ProjectId      UUID         [not null, ref: > RSS_Project.ProjectId, name: 'FK_RSS_UserProject_RSS_Project']
  CreatedOn      timestamp    [not null, default: 'now()']
  CreatedByName  varchar(50)  [null, default: 'System']
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}


Table RSS_UserAudit {
  UserAuditId    UUID         [not null, PK]
  UserId         UUID         [not null, ref: > RSS_User.UserId, name: 'FK_RSS_UserAudit_RSS_User']
  Action         varchar(100) [not null]
  CreatedOn      timestamp    [not null]
  CreatedByName  varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}


// - Home Design - //

Table RSS_StandardHomeModel {
  StandardHomeModelId          UUID          [not null, PK]
  ProjectId                    UUID          [not null, ref: > RSS_Project.ProjectId, name: 'FK_RSS_StandardHomeModel_RSS_Project']
  Title                        varchar(100)  [not null]
  Description                  varchar(500)  [null]
  Category                     varchar(200)  [null]
  MinLotWidth                  smallint      [null]
  Width                        decimal(12,2) [null]
  Depth                        decimal(12,2) [null]
  FloorArea                    smallint      [null]
  HouseArea                    decimal(12,2) [null]
  CarParkingArea               decimal(12,2) [null]
  OutdoorAlfrescoArea          decimal(12,2) [null]
  Storeys                      smallint      [null]
  NumberOfBedrooms             smallint      [null]
  NumberOfBathrooms            smallint      [null]
  NumberOfGarageSpots          smallint      [null]
  NatHERSClimateZone           smallint      [null]
  NorthOffset                  smallint      [null]
  BlockType                    varchar(200)  [null]
  VariableOptionsJson          varchar       [null]
  HomePlanImageId              UUID          [null]
  CreatedOn                    timestamp     [not null, default: 'now()']
  CreatedByName                varchar(50)   [null]
  ModifiedOn                   timestamp     [null]
  ModifiedByName               varchar(50)   [null]
  Deleted                      boolean       [not null, default: FALSE]
  View3dFloorPlans             boolean       [null]
  FloorplannerLink             varchar(200)  [null]
  VariationOptionsSettingsJson varchar       [null]
  IsVariationOfHomeModelId     UUID          [null, ref: > RSS_StandardHomeModel.StandardHomeModelId, name: 'FK_RSS_StandardHomeModel_RSS_StandardHomeModel_IsVariationOfHomeModelId']
  DrawingAreasJson             varchar       [null]
  ZoneSummaryBuildingDataJson  varchar       [null]
  ClientId                     UUID          [null, ref: > RSS_Client.ClientId, name: 'FK_RSS_StandardHomeModel_Client_ClientID']
  FeaturesMasterSuiteAtFront   boolean       [null]
  FeaturesMasterSuiteAtRear    boolean       [null]
  FeaturesMasterSuiteGroundFloor boolean     [null]
  FeaturesMasterSuiteFirstFloor boolean      [null]
  FeaturesHomeTheatre          boolean       [null]
  FeaturesActivity             boolean       [null]
  FeaturesGames                boolean       [null]
  FeaturesHomeOffice           boolean       [null]
  FeaturesScullery             boolean       [null]
  FeaturesOutdoorLiving        boolean       [null]
  FeaturesRHGarage             boolean       [null]
  FeaturesLHGarage             boolean       [null]
  FeaturesRearCarAccess        boolean       [null]
  IsActive                     boolean       [null]
  NccBuildingClassification    varchar(20)   [null]
  LowestLivingAreaFloorType    varchar(100)  [null]
  HabitableRoomFloorAreaM2     decimal(15,5) [null]
  CostEstimateEnabled          boolean       [null]
  CostEstimateDataJson         varchar       [null]
  VariableMetadataJson         varchar       [null]
  LivingAreas                  int           [null]
  FeaturesKitchenLivingDiningGroundFloor boolean [null]
  FeaturesKitchenLivingDiningUpperFloor boolean [null]
  FeaturesKitchenLivingDiningRear boolean    [null]
  FeaturesKitchenLivingDiningFront boolean   [null]
  FeaturesWalkInPantry         boolean       [null]
  FeaturesSecondLiving         boolean       [null]
  FeaturesRetreat              boolean       [null]
  FeaturesCarport              boolean       [null]
  FeaturesGarage               boolean       [null]
  FeaturesRearAccess           boolean       [null]
  HouseWidth                   decimal(15,5) [null]
  HouseDepth                   decimal(15,5) [null]
  GlassToHouseAreaRatio        decimal(15,5) [null]
  GlassToHousePerimiterRatio   decimal(15,5) [null]
  ConditionedFloorArea         decimal(15,5) [null]
  ConditionedNonHabitableFloorAreaRatio decimal(15,5) [null]
  GlassToInternalFloorAreaRatio decimal(15,5) [null]
  GlassToConditionedFloorAreaRatio decimal(15,5) [null]
  GlassToExteriorWallRatio     decimal(15,5) [null]
  FrontElevationWallRatio      decimal(15,5) [null]
  RearElevationWallRatio       decimal(15,5) [null]
  LeftElevationWallRatio       decimal(15,5) [null]
  RightElevationWallRatio      decimal(15,5) [null]
  FeaturesRLOutdoorLiving      boolean       [null]
  FeaturesRROutdoorLiving      boolean       [null]
  FeaturesCourtyard            boolean       [null]
  FeaturesRCOutdoorLiving      boolean       [null]
  CategoryDisplayHome          boolean       [null]
  CategoryFarmhouse            boolean       [null]
  CategoryNarrowLot            boolean       [null]
  CategoryNewDesign            boolean       [null]
  CategorySingleStorey         boolean       [null]
  CategoryTwoStorey            boolean       [null]
  CategoryThreeStorey          boolean       [null]
  SiteCover                    decimal(15,5) [null]
  WohFloorArea                 decimal(15,5) [null]
  CategoryGrannyFlat           boolean       [null]
  CategoryRearLoaded           boolean       [null]
  FeaturesDressingRoom         boolean       [null]
  FeaturesComputerNook         boolean       [null]
  FeaturesBalcony              boolean       [null]
  FeaturesVerandah             boolean       [null]
  FeaturesWorkshop             boolean       [null]
  FeaturesMultipurposeRoom     boolean       [null]
  DisplayFloorArea             decimal(15,5) [null]
  FloorAreaHabitableRooms      decimal(15,5) [null]
  HabitableRooms               int           [null]
  CategoryAcreage              boolean       [null]
  CategoryDualOccupancy        boolean       [null]
  CategoryDuplex               boolean       [null]
  CategorySplitLevel           boolean       [null]
  FeaturesKitchenLivingDiningMiddle boolean  [null]
  FeaturesButlersPantry        boolean       [null]
  FeaturesMasterSuiteAtMiddle  boolean       [null]
  FeaturesHisHerWir            boolean       [null]
  FeaturesWalkInRobe           boolean       [null]
  FeaturesMedia                boolean       [null]
  FeaturesEntertainment        boolean       [null]
  FeaturesFamily               boolean       [null]
  FeaturesFormalLounge         boolean       [null]
  FeaturesLeisure              boolean       [null]
  FeaturesLounge               boolean       [null]
  FeaturesRumpus               boolean       [null]
  FeaturesENook                boolean       [null]
  FeaturesStudy                boolean       [null]
  FeaturesStudyNook            boolean       [null]
  FeaturesCellar               boolean       [null]
  FeaturesCloakRoom            boolean       [null]
  FeaturesGuestBedroom         boolean       [null]
  FeaturesGym                  boolean       [null]
  FeaturesMudRoom              boolean       [null]
  FeaturesNannysQuarters       boolean       [null]
  FeaturesPowderRoom           boolean       [null]
  FeaturesStoreRoom            boolean       [null]
  FeaturesWalkInLinen          boolean       [null]
  FeaturesLHCarport            boolean       [null]
  FeaturesRHCarport            boolean       [null]
  FeaturesRearLoaded           boolean       [null]
  FeaturesAlfresco             boolean       [null]
  FeaturesMLAlfresco           boolean       [null]
  FeaturesMRAlfresco           boolean       [null]
  FeaturesRCAlfresco           boolean       [null]
  FeaturesRLAlfresco           boolean       [null]
  FeaturesRRAlfresco           boolean       [null]
  FeaturesFrontBalcony         boolean       [null]
  FeaturesRearBalcony          boolean       [null]
  FeaturesLHCourtyard          boolean       [null]
  FeaturesRHCourtyard          boolean       [null]
  FeaturesMLOutdoorLiving      boolean       [null]
  FeaturesMROutdoorLiving      boolean       [null]
  FeaturesHomeCinema           boolean       [null]
  VariationFloorplanId         UUID          [null, ref: > RSS_StandardHomeModelVariationOption.StandardHomeModelVariationOptionId, name: 'FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Floorplan']
  VariationDesignOptionId      UUID          [null, ref: > RSS_StandardHomeModelVariationOption.StandardHomeModelVariationOptionId, name: 'FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_DesignOption']
  VariationFacadeId            UUID          [null, ref: > RSS_StandardHomeModelVariationOption.StandardHomeModelVariationOptionId, name: 'FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Facade']
  VariationSpecificationId     UUID          [null, ref: > RSS_StandardHomeModelVariationOption.StandardHomeModelVariationOptionId, name: 'FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Specification']
  VariationConfigurationId     UUID          [null, ref: > RSS_StandardHomeModelVariationOption.StandardHomeModelVariationOptionId, name: 'FK_RSS_StandardHomeModel_RSS_StandardHomeModelVariationOption_Configuration']
  IsDefaultVariation           boolean       [null]
  SortOrder                    int           [null]
  DisplayFloorAreaVarRefJson   varchar       [null]
}
// INDEXES
//      - IX_RSS_StandardHomeModel_Deleted_ProjectId (Deleted, ProjectId, IsVariationOfHomeModelId, StandardHomeModelId)
//      - IX_RSS_StandardHomeModel_Deleted_IsVariationOfHomeModelId (Deleted, IsVariationOfHomeModelId, StandardHomeModelId)
//      - IX_RSS_StandardHomeModel_IsActive (IsActive, StandardHomeModelId)


Table RSS_StandardHomeModelOption {
  StandardHomeModelOptionId    UUID          [not null, PK]
  StandardHomeModelId          UUID          [not null, ref: > RSS_StandardHomeModel.StandardHomeModelId, name: 'FK_RSS_StandardHomeModelOption_StandardHomeModel_StandardHomeModelId']
  NatHERSClimateZone           tinyint       [not null]
  SiteExposure                 varchar(100)  [not null]
  FloorHeight                  decimal(12,6) [null]
  InteriorWallSolarAbsorptance decimal(15,5) [not null]
  FloorSolarAbsorptance        decimal(15,5) [not null]
  NorthOffset                  smallint      [not null]
  BlockType                    varchar(100)  [not null]
  RoofConstruction             varchar(100)  [not null]
  RoofInsulation               varchar(100)  [not null]
  RoofSolarAbsorptance         decimal(15,5) [not null]
  CeilingConstruction          varchar(100)  [not null]
  CeilingInsulation            varchar(100)  [not null]
  Comments                     varchar       [null]
  Active                       boolean       [not null]
  AssessmentMethod             varchar(100)  [null]
  CeilingFans                  varchar(100)  [not null, default: 'Nil']
  RecessedLightFittings        varchar(100)  [not null, default: 'Nil']
  ExteriorDoorSolarAbsorptance decimal(15,5) [not null]
  GarageDoorSolarAbsorptance   decimal(15,5) [not null]
}
// INDEXES
//      - IX_RSS_StandardHomeModelOption_SearchIndexA (ClimateZone, NorthOffset, RoofConstruction)
//      - IX_RSS_StandardHomeModelOption_ByRow (Row)
//      - IX_RSS_StandardHomeModelOption_SearchIndexA_ByStandardModel (StandardHomeModelId, ClimateZone, NorthOffset)
//      - IX_RSS_StandardHomeModelOption_StandardHomeModelId (StandardHomeModelId, AssessmentMethod, NatHERSClimateZone, NorthOffset, RoofConstruction, CeilingInsulation, InteriorWallInsulation)


Table RSS_StandardHomeModelFile {
  StandardHomeModelFileId UUID         [not null, PK]
  StandardHomeModelId     UUID         [not null, ref: > RSS_StandardHomeModel.StandardHomeModelId, name: 'FK_RSS_StandardHomeModelFile_StandardHomeModel_StandardHomeModelId']
  FileId                  UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_StandardHomeModelFile_File_FileId']
  MetadataJson            varchar      [null]
  Deleted                 boolean      [not null]
  CreatedOn               timestamp    [not null]
  CreatedByName           varchar(100) [null]
  ModifiedOn              timestamp    [null]
  ModifiedByName          varchar(100) [null]
  SortOrder               smallint     [null]
  Label                   varchar(100) [null]
}
// INDEXES
//      - IX_RSS_StandardHomeModelFile_StandardModelId (StandardHomeModelId)
//      - IX_RSS_StandardHomeModelFile_Deleted (Deleted, StandardHomeModelId)


Table RSS_StandardHomeModelVariationOption {
  StandardHomeModelVariationOptionId UUID         [not null, PK]
  StandardHomeModelId                UUID         [not null, ref: > RSS_StandardHomeModel.StandardHomeModelId, name: 'FK_RSS_StandardHomeModelVariationOption_RSS_StandardHomeModel']
  VariationCategoryCode              varchar(100) [not null]
  OptionName                         varchar(200) [not null]
  SortOrder                          smallint     [not null]
  Deleted                            boolean      [not null, default: FALSE]
}


// - Assessment - //

Table RSS_Assessment {
  AssessmentId                    UUID         [not null, PK]
  JobId                           UUID         [null, ref: > RSS_Job.JobId, name: 'FK_RSS_Assessment_RSS_Job']
  StatusCode                      varchar(20)  [not null, ref: > RSS_Status.StatusCode, name: 'FK_RSS_Assessment_RSS_Status']
  NCCClimateZoneCode              varchar(20)  [null, ref: > RSS_NCCClimateZone.NCCClimateZoneCode, name: 'FK_RSS_Assessment_RSS_NCCClimateZone']
  NatHERSClimateZoneCode          varchar(20)  [null, ref: > RSS_NatHERSClimateZone.NatHERSClimateZoneCode, name: 'FK_RSS_Assessment_RSS_NatHERSClimateZone']
  BuildingExposureCode            varchar(20)  [null, ref: > RSS_BuildingExposure.BuildingExposureCode, name: 'FK_RSS_Assessment_RSS_BuildingExposure']
  PerformanceRequirementP261Code  varchar(20)  [null, ref: > RSS_PerformanceRequirementP261.PerformanceRequirementP261Code, name: 'FK_RSS_Assessment_RSS_PerformanceRequirementP261']
  PerformanceRequirementP262Code  varchar(20)  [not null, ref: > RSS_PerformanceRequirementP262.PerformanceRequirementP262Code, name: 'FK_RSS_Assessment_RSS_PerformanceRequirementP262']
  ComplianceStatusCode            varchar(20)  [null, ref: > RSS_ComplianceStatus.ComplianceStatusCode, name: 'FK_RSS_Assessment_RSS_ComplianceStatus']
  CertificateNumber               varchar(60)  [null]
  CerficateDate                   timestamp    [null]
  AssessorSignatureFile           UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_Assessment_RSS_AssessorSignatureFile']
  SoftwareFileId                  UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_Assessment_RSS_SoftwareFile']
  CreatedOn                       timestamp    [not null]
  CreatedByName                   varchar(50)  [null]
  ModifiedOn                      timestamp    [null]
  ModifiedByName                  varchar(50)  [null]
  Deleted                         boolean      [not null]
  BushfireAttackLevelCode         varchar(20)  [null, ref: > RSS_BushfireAttackLevel.BushfireAttackLevelCode, name: 'FK_RSS_Assessment_RSS_BushfireAttackLevel']
  Notes                           varchar      [null]
  AssessmentTemplateId            UUID         [null, ref: > RSS_Template.TemplateId, name: 'FK_RSS_Assessment_Template']
  PriorityCode                    varchar(20)  [null, ref: > RSS_Priority.PriorityCode, name: 'FK_RSS_Assessment_RSS_Priority']
  AssessmentSoftwareOther         varchar(250) [null]
  SummaryReport                   boolean      [not null]
  IncludeMapInExportedPDF         boolean      [not null]
  IsBushFireProne                 boolean      [null]
  AssessorUserId                  UUID         [null, ref: > RSS_User.UserId, name: 'FK_RSS_Assessment_RSS_User_AssessorUserId']
  WorksDescriptionCode            varchar(20)  [null, ref: > WorksDescriptionCode.WorksDescriptionCode, name: 'FK_RSS_Assessment_WorksDescriptionCode']
  WorksDescriptionOther           varchar(100) [null]
  AssessorNotesNotApplicable      boolean      [null]
  AssessorNotes                   varchar(4000) [null]
  RecertificationDesignChangesJson varchar     [null]
  ComfortMetricsJson              varchar      [null]
  LightAndVentilationCertificateNumber varchar(60) [null]
  CertificateDateOverride         timestamp    [null]
  BushFireProneUnknown            boolean      [null]
}
// INDEXES
//      - IX_RSS_Assessment_CreatedOn (CreatedOn, Deleted)
//      - IX_RSS_Assessment_StatusCode (StatusCode, Deleted)
//      - IX_RSS_Assessment_JobId (JobId, Deleted)
//      - IX_RSS_Assessment_AssessorEmployeeId (AssessorEmployeeId, Deleted)


// - Reference Tables - //

Table RSS_RunFrequencyCode {
  RunFrequencyCode varchar(20)  [not null, PK]
  Description      varchar(100) [null]
  Deleted          boolean      [not null, default: FALSE]
}


Table RSS_BatchJobQueue {
  RecId                 int          [not null, PK]
  RequestName           varchar(200) [null]
  RequestData           varchar      [null]
  RequestedDateTimeUtc  timestamp    [null]
  Submitted             boolean      [not null, default: FALSE]
  DueDateTimeUtc        timestamp    [null]
  RunFrequencyCode      varchar(20)  [null, ref: > RSS_RunFrequencyCode.RunFrequencyCode, name: 'FK_RSS_BatchJobQueue_RSS_RunFrequencyCode']
  RunAtTime             varchar(10)  [null]
  LastRunDateTimeUtc    timestamp    [null]
}

Table RSS_SlipAddressDataB {
  SlipAddressDataId     UUID         [not null, PK]
  LandId                varchar(50)  [null]
  SurveyType            varchar(50)  [null]
  SurveyNumber          varchar(50)  [null]
  LotNumber             varchar(50)  [null]
  UnitType              varchar(50)  [null]
  UnitNumber            varchar(50)  [null]
  RoadNumber            varchar(50)  [null]
  RoadName              varchar(200) [null]
  RoadType              varchar(50)  [null]
  Locality              varchar(100) [null]
  PostCode              varchar(10)  [null]
  FormattedAddress      varchar(500) [null]
  Latitude              decimal(14,10) [null]
  Longitude             decimal(14,10) [null]
  LandArea              decimal(15,5) [null]
  TitleIdentifier       varchar(100) [null]
  ProjectOwner          varchar(200) [null]
  Boundary              varchar      [null]
  BoundaryString        varchar      [null]
  BoundaryJson          varchar      [null]
}

Table RSS_WholeOfHomeEfficiencyFactor {
  Identifier                                    varchar(50)  [not null, PK]
  StateOfAustralia                              varchar(20)  [null]
  NccClimateZone                                varchar(20)  [null]
  SpaceHeatingSystemTypeCode                    varchar(50)  [null, ref: > ServiceType.ServiceTypeCode, name: 'FK_RSS_WholeOfHomeEfficiencyFactor_SpaceHeatingSystemTypeCode_ServiceType_ServiceTypeCode']
  SpaceHeatingSystemMinEnergyRatingGems2012     decimal(4,1) [null]
  SpaceHeatingSystemMaxEnergyRatingGems2012     decimal(4,1) [null]
  SpaceHeatingSystemMinEnergyRatingGems2019     decimal(4,1) [null]
  SpaceHeatingSystemMaxEnergyRatingGems2019     decimal(4,1) [null]
  SpaceCoolingSystemTypeCode                    varchar(50)  [null, ref: > ServiceType.ServiceTypeCode, name: 'FK_RSS_WholeOfHomeEfficiencyFactor_SpaceCoolingSystemTypeCode_ServiceType_ServiceTypeCode']
  SpaceCoolingSystemMinEnergyRatingGems2012     decimal(4,1) [null]
  SpaceCoolingSystemMaxEnergyRatingGems2012     decimal(4,1) [null]
  SpaceCoolingSystemMinEnergyRatingGems2019     decimal(4,1) [null]
  SpaceCoolingSystemMaxEnergyRatingGems2019     decimal(4,1) [null]
  ElectricStorageStandard                       decimal(15,5) [null]
  ElectricStorageOffPeak                        decimal(15,5) [null]
  HeatPumpStandard                              decimal(15,5) [null]
  HeatPumpOffPeak                               decimal(15,5) [null]
  SolarElectricStandard                         decimal(15,5) [null]
  GasStorage                                    decimal(15,5) [null]
  GasInstantaneous                              decimal(15,5) [null]
  SolarGas                                      decimal(15,5) [null]
  OtherOrNoneSpecified                          decimal(15,5) [null]
}

Table RSS_File {
  FileId                        UUID         [not null, PK]
  DisplayName                   varchar(200) [null]
  FileName                      varchar(200) [not null]
  FolderName                    varchar(200) [null]
  URL                           varchar(500) [null]
  FileExtension                 varchar(10)  [not null]
  FileSize                      int          [not null]
  SizeInBytes                   bigint       [null]
  MimeType                      varchar(100) [not null]
  CreatedOn                     timestamp    [not null, default: 'now()']
  CreatedByName                 varchar(50)  [null]
  ModifiedOn                    timestamp    [null]
  ModifiedByName                varchar(50)  [null]
  Deleted                       boolean      [not null, default: FALSE]
  VersionNo                     int          [null]
  JobId                         UUID         [null, ref: > RSS_Job.JobId, name: 'FK_RSS_File_RSS_Job']
  AssessmentId                  UUID         [null, ref: > RSS_Assessment.AssessmentId, name: 'FK_RSS_File_RSS_Assessment']
  PageCount                     int          [null]
  OriginalFileUrl               varchar(500) [null]
  SortOrder                     int          [null]
  ShowOnClientPortal            boolean      [null]
  AllowDownloadOnClientPortal   boolean      [null]
  ComplianceOptionId            UUID         [null, ref: > RSS_AssessmentComplianceOption.ComplianceOptionsId, name: 'FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId']
  Category                      varchar(100) [null]
  Classification                varchar(100) [null]
  BuildingFileId                UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_FILE_BuildingFile']
  DataFileId                    UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_FILE_DataFile']
  MarkupFileId                  UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_FILE_MarkupFile']
  PurchaseOrderFileId           UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_FILE_PurchaseOrderFile']
  SoftwareFileId                UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_FILE_SoftwareFile']
  OutputFileId                  UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_File_RSS_AssessmentComplianceBuilding_OutputFile']
  OutputSummaryFileId           UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_File_RSS_AssessmentComplianceBuilding_OutputSummaryFile']
}
// INDEXES
//      - IX_RSS_File_JobId (JobId, FileId)
//      - IX_RSS_File_AssessmentId (AssessmentId, FileId)


Table RSS_FileVersion {
  FileId        UUID         [not null, PK]
  VersionNo     int          [not null, PK]
  CreatedOn     timestamp    [not null, default: 'now()']
  CreatedByName varchar(150) [null]
  CreatedBy     UUID         [null]
  Deleted       boolean      [not null, default: FALSE]
}

Ref: RSS_FileVersion.FileId > RSS_File.FileId [delete: cascade, name: 'FK_RSS_FileVersion_RSS_File']


Table RSS_Country {
  CountryCode varchar(20)  [not null, PK]
  Name        varchar(100) [not null]
  Deleted     boolean      [not null, default: FALSE]
}


Table RSS_State {
  StateCode                     varchar(20) [not null, PK]
  Name                          varchar(50) [not null]
  CountryCode                   varchar(20) [null, ref: > RSS_Country.CountryCode, name: 'FK_RSS_State_RSS_Country']
  Deleted                       boolean     [not null]
  HeatingAndCoolingRulesetCode  varchar(30) [null, ref: > RSS_Ruleset_HeatingAndCoolingRuleset.HeatingAndCoolingRulesetCode, name: 'FK_RSS_State_RSS_Ruleset_HeatingAndCoolingRuleset']
}


Table RSS_Status {
  StatusCode     varchar(20)  [not null, PK]
  Description    varchar(120) [not null]
  Deleted        boolean      [not null]
  SortOrder      int          [not null]
  Hidden         boolean      [not null]
  StatusTypeCode varchar(20)  [null]
}


Table RSS_NatHERSClimateZone {
  NatHERSClimateZoneCode varchar(20) [not null, PK]
  Description            varchar(20) [null]
  CreatedOn              timestamp   [not null, default: 'now()']
  CreatedByName          varchar(50) [null]
  ModifiedOn             timestamp   [null]
  ModifiedByName         varchar(50) [null]
  Deleted                boolean     [not null, default: FALSE]
}


Table NatHERSClimateZonePostCode {
  PostCodeId             UUID         [not null, PK]
  PostCode               varchar(10)  [not null]
  NatHERSClimateZoneCode varchar(20)  [not null, ref: > RSS_NatHERSClimateZone.NatHERSClimateZoneCode, name: 'FK_NatHERSClimateZonePostCode_RSS_NatHERSClimateZone']
  Deleted                boolean      [not null, default: FALSE]
}


Table RSS_NCCClimateZone {
  NCCClimateZoneCode varchar(20) [not null, PK]
  Description        varchar(20) [null]
  CreatedOn          timestamp   [not null, default: 'now()']
  CreatedByName      varchar(50) [null]
  ModifiedOn         timestamp   [null]
  ModifiedByName     varchar(50) [null]
  Deleted            boolean     [not null, default: FALSE]
}


Table RSS_BuildingExposure {
  BuildingExposureCode varchar(20)  [not null, PK]
  Description          varchar(100) [null]
  CreatedOn            timestamp    [not null, default: 'now()']
  CreatedByName        varchar(50)  [null]
  ModifiedOn           timestamp    [null]
  ModifiedByName       varchar(50)  [null]
  Deleted              boolean      [not null, default: FALSE]
}


Table RSS_AssessmentSoftware {
  AssessmentSoftwareCode varchar(20)  [not null, PK]
  Description            varchar(100) [null]
  CreatedOn              timestamp    [not null, default: 'now()']
  CreatedByName          varchar(50)  [null]
  ModifiedOn             timestamp    [null]
  ModifiedByName         varchar(50)  [null]
  Deleted                boolean      [not null, default: FALSE]
}


Table RSS_AssessmentSoftwareComplianceMethod {
  AssessmentSoftwareComplianceMethodId UUID         [not null, PK]
  AssessmentSoftwareCode               varchar(20)  [not null, ref: > RSS_AssessmentSoftware.AssessmentSoftwareCode, name: 'FK_RSS_AssessmentSoftwareComplianceMethod_RSS_AssessmentSoftware']
  ComplianceMethodCode                 varchar(20)  [not null, ref: > RSS_ComplianceMethod.ComplianceMethodCode, name: 'FK_RSS_AssessmentSoftwareComplianceMethod_RSS_ComplianceMethod']
  Deleted                              boolean      [not null, default: FALSE]
}


Table RSS_Priority {
  PriorityCode varchar(20)  [not null, PK]
  Description  varchar(100) [null]
  SortOrder    int          [not null]
  Deleted      boolean      [not null, default: FALSE]
}


Table RSS_BushfireAttackLevel {
  BushfireAttackLevelCode varchar(20)  [not null, PK]
  Description             varchar(100) [null]
  Deleted                 boolean      [not null, default: FALSE]
}


Table RSS_BushfireStateData {
  BushfireStateDataId UUID         [not null, PK]
  StateCode           varchar(20)  [not null, ref: > RSS_State.StateCode, name: 'FK_RSS_BushfireStateData_RSS_State']
  Description         varchar(100) [null]
  Deleted             boolean      [not null, default: FALSE]
}


Table RSS_PerformanceRequirementP261 {
  PerformanceRequirementP261Code varchar(20)  [not null, PK]
  Description                    varchar(100) [null]
  Deleted                        boolean      [not null, default: FALSE]
}


Table RSS_PerformanceRequirementP262 {
  PerformanceRequirementP262Code varchar(20)  [not null, PK]
  Description                    varchar(100) [null]
  Deleted                        boolean      [not null, default: FALSE]
}


Table WorksDescriptionCode {
  WorksDescriptionCode varchar(20)  [not null, PK]
  Description          varchar(100) [null]
  Deleted              boolean      [not null, default: FALSE]
}


Table RSS_Certification {
  CertificationId                UUID         [not null, PK]
  Description                    varchar(100) [null]
  ChenathRulesetCode             varchar(20)  [null, ref: > RSS_Ruleset_ChenathRuleset.ChenathRulesetCode, name: 'FK_RSS_Certification_RSS_Ruleset_ChenathRuleset']
  DecimalStarbandRulesetCode     varchar(20)  [null, ref: > RSS_Ruleset_DecimalStarbandRuleset.DecimalStarbandRulesetCode, name: 'FK_RSS_Certification_RSS_Ruleset_DecimalStarbandRuleset']
  GlazingCalculatorRulesetCode   varchar(20)  [null, ref: > RSS_Ruleset_GlazingCalculatorRuleset.GlazingCalculatorRulesetCode, name: 'FK_RSS_Certification_RSS_Ruleset_GlazingCalculatorRuleset']
  HeatingAndCoolingRulesetCode   varchar(20)  [null, ref: > RSS_Ruleset_HeatingAndCoolingRuleset.HeatingAndCoolingRulesetCode, name: 'FK_RSS_Certification_RSS_Ruleset_HeatingAndCoolingRuleset']
  SectorDeterminationCode        varchar(20)  [null, ref: > RSS_SectorDetermination.SectorDeterminationCode, name: 'FK_RSS_Certification_RSS_SectorDetermination_RSS_SectorDeterminationCode']
  Deleted                        boolean      [not null, default: FALSE]
}


Table RSS_Ruleset_ChenathRuleset {
  ChenathRulesetCode varchar(20)  [not null, PK]
  Description        varchar(100) [null]
  Deleted            boolean      [not null, default: FALSE]
}


Table RSS_Ruleset_DecimalStarbandRuleset {
  DecimalStarbandRulesetCode varchar(20)  [not null, PK]
  Description                varchar(100) [null]
  Deleted                    boolean      [not null, default: FALSE]
}


Table RSS_Ruleset_GlazingCalculatorRuleset {
  GlazingCalculatorRulesetCode varchar(20)  [not null, PK]
  Description                  varchar(100) [null]
  Deleted                      boolean      [not null, default: FALSE]
}


Table RSS_Ruleset_HeatingAndCoolingRuleset {
  HeatingAndCoolingRulesetCode varchar(20)  [not null, PK]
  Description                  varchar(100) [null]
  Deleted                      boolean      [not null, default: FALSE]
}


Table RSS_SectorDetermination {
  SectorDeterminationCode varchar(20)  [not null, PK]
  Description             varchar(100) [null]
  Deleted                 boolean      [not null, default: FALSE]
}


// - Assessment Related Tables - //

Table RSS_AssessmentDrawing {
  AssessmentDrawingId UUID         [not null, PK]
  AssessmentId        UUID         [not null, ref: > RSS_Assessment.AssessmentId, name: 'FK_RSS_AssessmentDrawing_RSS_Assessment']
  DrawingNumber       int          [null]
  DrawingDescription  varchar(100) [null]
  Attachment          UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentDrawing_RSS_File']
  SheetNumber         int          [null]
  Revision            varchar(20)  [null]
  RevisionDate        timestamp    [null]
  IsIncludedInReport  boolean      [not null, default: FALSE]
  CreatedOn           timestamp    [not null]
  CreatedByName       varchar(50)  [null]
  ModifiedOn          timestamp    [null]
  ModifiedByName      varchar(50)  [null]
  Deleted             boolean      [not null, default: FALSE]
  ToStamp             boolean      [not null, default: TRUE]
}
// INDEXES
//      - IX_RSS_AssessmentDrawing_AssessmentId (AssessmentId, Deleted)


Table RSS_AssessmentProjectDetail {
  AssessmentId                UUID           [not null, PK, ref: > RSS_Assessment.AssessmentId, name: 'FK_RSS_AssessmentProjectDetail_RSS_Assessment']
  Latitude                    decimal(14,10) [null]
  Longitude                   decimal(14,10) [null]
  LotTypeCode                 varchar(20)    [null, ref: > RSS_LotType.LotTypeCode, name: 'FK_RSS_AssessmentProjectDetail_RSS_LotType']
  Prefix                      varchar(20)    [null]
  StrataLotNumber             int            [null]
  SurveyStrataLotNumber       int            [null]
  OriginalLotNumber           int            [null]
  LotNumber                   int            [null]
  DepositedPlanNumber         int            [null]
  Volume                      varchar(40)    [null]
  Folio                       varchar(40)    [null]
  HouseNumber                 varchar(20)    [null]
  StreetName                  varchar(400)   [null]
  StreetType                  varchar(100)   [null]
  SuburbName                  varchar(100)   [null]
  StateCode                   varchar(20)    [null]
  Postcode                    varchar(20)    [null]
  ContactId                   UUID           [null, ref: > RSS_Contact.ContactId, name: 'FK_RSS_AssessmentProjectDetail_ContactId']
  OrderTypeCode               varchar(20)    [null, ref: > RSS_OrderType.OrderTypeCode, name: 'FK_RSS_AssessmentProjectDetail_RSS_OrderType']
  ClientAssigneeUserId        UUID           [null, ref: > RSS_User.UserId, name: 'FK_RSS_AssessmentProjectDetail_RSS_User_ClientAssigneeUserId']
  SiteMapImageId              UUID           [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentProjectDetail_SiteMapImage']
  PlanFileId                  UUID           [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentProjectDetail_RSS_File_1']
  CreatedOn                   timestamp      [not null]
  CreatedByName               varchar(50)    [null]
  ModifiedOn                  timestamp      [null]
  ModifiedByName              varchar(50)    [null]
  Deleted                     boolean        [not null, default: FALSE]
}
// INDEXES
//      - IX_RSS_AssessmentProjectDetail_ClientJobNumber (ClientJobNumber)


Table RSS_AssessmentComplianceOption {
  ComplianceOptionsId          UUID         [not null, PK]
  AssessmentId                 UUID         [not null, ref: > RSS_Assessment.AssessmentId, name: 'FK_RSS_AssessmentComplianceOption_RSS_Assessment']
  ComplianceMethodCode         varchar(20)  [not null, ref: > RSS_ComplianceMethod.ComplianceMethodCode, name: 'FK_RSS_AssessmentComplianceOption_RSS_ComplianceMethod']
  Description                  varchar(100) [not null]
  Heating                      decimal(15,5) [null]
  Cooling                      decimal(15,5) [null]
  Total                        decimal(15,5) [null]
  HouseEnergyRating            decimal(15,5) [null]
  MarkupFileId                 UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceOption_RSS_MarkupFile']
  SoftwareFileId               UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceOption_RSS_SoftwareFile']
  IsSelected                   boolean      [not null, default: FALSE]
  OptionIndex                  int          [null]
  CreatedOn                    timestamp    [not null]
  CreatedByName                varchar(50)  [null]
  ModifiedOn                   timestamp    [null]
  ModifiedByName               varchar(50)  [null]
  Deleted                      boolean      [not null, default: FALSE]
  IsCompliant                  boolean      [null]
  RequiredHouseEnergyRating    decimal(15,5) [null]
  ItemReference                varchar(100) [null]
  IsLocked                     boolean      [null]
  EPComplianceDataJson         varchar      [null]
  AssessmentSoftwareCode       varchar(20)  [null, ref: > RSS_AssessmentSoftware.AssessmentSoftwareCode, name: 'FK_RSS_AssessmentComplianceOption_RSS_AssessmentSoftware']
  NewPurchaseOrderRequired     boolean      [null]
  UpdatedDrawingsRequired      boolean      [null]
  MarkupFileRequired           boolean      [null]
  ProposedBuildingId           UUID         [null, ref: > RSS_AssessmentComplianceBuilding.AssessmentComplianceBuildingId, name: 'FK_RSS_ACO_ProposedBuilding']
  ReferenceBuildingId          UUID         [null, ref: > RSS_AssessmentComplianceBuilding.AssessmentComplianceBuildingId, name: 'FK_RSS_ACO_ReferenceBuilding']
  PurchaseOrderFileId          UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceOption_RSS_File_PurchaseOrderFileId']
  IsBaselineSimulation         boolean      [null]
  PurchaseOrder                varchar(200) [null]
  HeatingAndCoolingRulesetCode varchar(20)  [null, ref: > RSS_Ruleset_HeatingAndCoolingRuleset.HeatingAndCoolingRulesetCode, name: 'FK_RSS_AssessmentComplianceOption_RSS_Ruleset_HeatingAndCoolingRulesetCode']
  CertificationId              UUID         [null, ref: > RSS_Certification.CertificationId, name: 'FK_RSS_AssessmentComplianceOption_RSS_Certification_CertificationId']
  SectorDeterminationCode      varchar(20)  [null, ref: > RSS_SectorDetermination.SectorDeterminationCode, name: 'FK_RSS_AssessmentComplianceOption_RSS_SectorDetermination_SectorDeterminationCode']
  IsComplianceValid            boolean      [null]
  NCC2022Json                  varchar      [null]
  IsShownToClient              boolean      [null]
}
// INDEXES
//      - IX_RSS_AssessmentComplianceOption_AssessmentId (AssessmentId, Deleted)


Table RSS_AssessmentComplianceBuilding {
  AssessmentComplianceBuildingId UUID         [not null, PK]
  AssessmentComplianceOptionId   UUID         [not null, ref: > RSS_AssessmentComplianceOption.ComplianceOptionsId, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_AssessmentComplianceOption']
  Description                    varchar(500) [null]
  StoreysJson                    varchar      [null]
  FileBId                        UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_File_FileBId']
  MarkupFileId                   UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_File_MarkupFileId']
  FileAId                        UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_File_FileAId']
  FileCId                        UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_File_FileCId']
  FileDId                        UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_File_FileDId']
  FileEId                        UUID         [null, ref: > RSS_File.FileId, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_File_FileEId']
  HouseEnergyRating              decimal(15,5) [null]
  Heating                        decimal(15,5) [null]
  Cooling                        decimal(15,5) [null]
  TotalEnergyLoad                decimal(15,5) [null]
  ConditionedFloorArea           decimal(15,5) [null]
  UnconditionedFloorArea         decimal(15,5) [null]
  AttachedGarageFloorArea        decimal(15,5) [null]
  SurfacesJson                   varchar      [null]
  OpeningsJson                   varchar      [null]
  ServicesJson                   varchar      [null]
  ConstructionTemplateTitle      varchar(200) [null]
  IncludeBuildingElementsInReport boolean     [null]
  BuildingZonesTemplateId        UUID         [null]
  ConstructionTemplateId         UUID         [null]
  GlazingCalcFilesJson           varchar      [null]
  CategoriesWithExternalDataJson varchar      [null]
  LowestLivingAreaFloorType      varchar(100) [null]
  Classification                 varchar(100) [null]
  MasonryWalls                   boolean      [null]
  OpeningSpecification           varchar(100) [null]
  OpeningTemplateTitle           varchar(200) [null]
  OpeningTemplateId              UUID         [null]
  ProjectClassification          varchar(100) [null]
  ProjectDescriptionCode         varchar(20)  [null, ref: > RSS_ProjectDescription.ProjectDescriptionCode, name: 'FK_RSS_AssessmentComplianceBuilding_RSS_ProjectDescription']
  ProjectDescriptionOther        varchar(200) [null]
  DesignFeaturesJson             varchar      [null]
  DesignWasBlankFromTemplate     boolean      [null]
  Design                         varchar(200) [null]
  ServicesTemplateId             UUID         [null]
  ServicesTemplateTitle          varchar(200) [null]
  CategoriesNotRequiredJson      varchar      [null]
  BuildingOrientation            decimal(15,5) [null]
  ZoneTypesNotApplicableJson     varchar      [null]
  EnergyUsageSummaryJson         varchar      [null]
  Ncc2019AreaCorrectionFactor    decimal(15,5) [null]
  Ncc2022AreaCorrectionFactor    decimal(15,5) [null]
  GarageLocation                 varchar(100) [null]
  BuildingWidth                  decimal(15,5) [null]
  BuildingLength                 decimal(15,5) [null]
  BuildingPerimeter              decimal(15,5) [null]
  RoofArea                       decimal(15,5) [null]
  SpacesJson                     varchar      [null]
  HeatingLoadLimitCorrectionFactor decimal(15,5) [null]
  CoolingLoadLimitCorrectionFactor decimal(15,5) [null]
  HeatingOriginal                decimal(15,5) [null]
  CoolingOriginal                decimal(15,5) [null]
  TotalEnergyLoadOriginal        decimal(15,5) [null]
  HouseEnergyRatingOverride      decimal(15,5) [null]
  OverrideEnergyLoads            boolean      [null]
  EnergyResultsChartDataJson     varchar      [null]
  EnvelopeSummaryJson            varchar      [null]
  ZoneSummaryJson                varchar      [null]
  EnvelopeSummaryConditionedJson varchar      [null]
  EnvelopeSummaryHabitableJson   varchar      [null]
  CreatedOn                      timestamp    [not null]
  CreatedByName                  varchar(50)  [not null]
  ModifiedOn                     timestamp    [null]
  ModifiedByName                 varchar(50)  [null]
  Deleted                        boolean      [not null, default: FALSE]
}
// INDEXES
//      - IX_AssessmentComplianceBuilding_AssessmentComplianceOption (AssessmentComplianceOptionId)


// - Options Tables - //

Table RSS_ComplianceMethod {
  ComplianceMethodCode varchar(20)  [not null, PK]
  Description          varchar(100) [null]
  AspNetUserId         varchar(128) [null, ref: > AspNetUsers.Id, name: 'FK_RSS_ComplianceMethod_AspNetUserId']
  CreatedOn            timestamp    [not null]
  CreatedByName        varchar(50)  [null]
  ModifiedOn           timestamp    [null]
  ModifiedByName       varchar(50)  [null]
  Deleted              boolean      [not null, default: FALSE]
}


Table RSS_ComplianceStatus {
  ComplianceStatusCode varchar(20)  [not null, PK]
  Description          varchar(100) [null]
  CreatedOn            timestamp    [not null]
  CreatedByName        varchar(50)  [null]
  ModifiedOn           timestamp    [null]
  ModifiedByName       varchar(50)  [null]
  Deleted              boolean      [not null, default: FALSE]
}


Table RSS_LotType {
  LotTypeCode varchar(20)  [not null, PK]
  Description varchar(100) [null]
  CreatedOn   timestamp    [not null]
  CreatedByName varchar(50) [null]
  ModifiedOn  timestamp    [null]
  ModifiedByName varchar(50) [null]
  Deleted     boolean      [not null, default: FALSE]
}


Table RSS_OrderType {
  OrderTypeCode varchar(20)  [not null, PK]
  Description   varchar(100) [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_Contact {
  ContactId     UUID         [not null, PK]
  FirstName     varchar(100) [null]
  LastName      varchar(100) [null]
  Email         varchar(150) [null]
  Phone         varchar(20)  [null]
  CreatedOn     timestamp    [not null]
  CreatedByName varchar(50)  [null]
  ModifiedOn    timestamp    [null]
  ModifiedByName varchar(50) [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_StreetType {
  StreetTypeCode varchar(100) [not null, PK]
  Description    varchar(100) [null]
  CreatedOn      timestamp    [not null]
  CreatedByName  varchar(50)  [null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}


Table RSS_Suburb {
  SuburbCode           UUID         [not null, PK]
  Name                 varchar(100) [not null]
  StateCode            varchar(20)  [null, ref: > RSS_State.StateCode, name: 'FK_RSS_Suburb_RSS_State']
  Postcode             varchar(20)  [null]
  LocalGovernmentArea  varchar(250) [not null]
  LocalityId           varchar(100) [null]
  CreatedOn            timestamp    [not null]
  CreatedByName        varchar(50)  [null]
  ModifiedOn           timestamp    [null]
  ModifiedByName       varchar(50)  [null]
  Deleted              boolean      [not null, default: FALSE]
}


// - Template Tables - //

Table RSS_Template {
  TemplateId         UUID         [not null, PK]
  TemplateCategoryId UUID         [not null, ref: > RSS_TemplateCategory.TemplateCategoryId, name: 'FK_RSS_Template_RSS_TemplateCategory']
  Name               varchar(100) [not null]
  Description        varchar(500) [null]
  Deleted            boolean      [not null, default: FALSE]
}


Table RSS_TemplateCategory {
  TemplateCategoryId UUID         [not null, PK]
  Name               varchar(100) [not null]
  Description        varchar(500) [null]
  Deleted            boolean      [not null, default: FALSE]
}


// - Construction and Template Tables - //

Table RSS_ConstructionCategory {
  ConstructionCategoryCode varchar(20)  [not null, PK]
  Description              varchar(100) [null]
  SortOrder                smallint     [not null]
  Deleted                  boolean      [not null, default: FALSE]
  Type                     varchar(100) [not null]
}


Table RSS_Manufacturer {
  ManufacturerId UUID         [not null, PK]
  Description    varchar(100) [not null]
  CreatedOn      timestamp    [not null]
  CreatedByName  varchar(50)  [null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}


Table RSS_Supplier {
  SupplierId     UUID         [not null, PK]
  Description    varchar(100) [not null]
  CreatedOn      timestamp    [not null]
  CreatedByName  varchar(50)  [null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}


Table RSS_Colour {
  ColourId       UUID         [not null, PK]
  ManufacturerId UUID         [not null, ref: > RSS_Manufacturer.ManufacturerId, name: 'FK_RSS_Colour_RSS_Manufacturer']
  SupplierId     UUID         [null, ref: > RSS_Supplier.SupplierId, name: 'FK_RSS_Colour_RSS_Supplier_ID']
  Name           varchar(100) [not null]
  ColourCode     varchar(50)  [null]
  CreatedOn      timestamp    [not null]
  CreatedByName  varchar(50)  [null]
  ModifiedOn     timestamp    [null]
  ModifiedByName varchar(50)  [null]
  Deleted        boolean      [not null, default: FALSE]
}


Table RSS_OpeningTemplate {
  ConstructionId           UUID           [not null, PK]
  Deleted                  boolean        [not null]
  CreatedOn                timestamp      [not null]
  CreatedByName            varchar(50)    [null]
  ModifiedOn               timestamp      [null]
  ModifiedByName           varchar(50)    [null]
  Description              varchar(100)   [null]
  DisplayDescription       varchar(100)   [null]
  ManufacturerId           UUID           [null, ref: > RSS_Manufacturer.ManufacturerId, name: 'FK_RSS_OpeningTemplate_RSS_Manufacturer']
  ExternalConstructionId   varchar(100)   [null]
  AdjacencyCode            varchar(20)    [null, ref: > RSS_Adjacency.AdjacencyCode, name: 'FK_RSS_OpeningTemplate_RSS_Adjacency']
  UnitOfMeasureCode        varchar(20)    [null, ref: > RSS_UnitOfMeasure.UnitOfMeasureCode, name: 'FK_RSS_OpeningTemplate_RSS_UnitOfMeasure']
  Comments                 varchar        [null]
  LifeCycleDataJson        varchar        [null]
  ChenathDataJson          varchar        [null]
  FR5DataJson              varchar        [null]
  HeroDataJson             varchar        [null]
  EPDataJson               varchar        [null]
  ConstructionCategoryCode varchar(20)    [null, ref: > RSS_ConstructionCategory.ConstructionCategoryCode, name: 'FK_RSS_OpeningTemplate_RSS_ConstructionCategory']
  FrameMaterialCode        varchar(20)    [null, ref: > RSS_FrameMaterial.FrameMaterialCode, name: 'FK_RSS_OpeningTemplate_RSS_FrameMaterial']
  IsThermalBreak           boolean        [null]
  Tilt                     decimal(15,5)  [null]
  OpeningStyleCode         varchar(20)    [null, ref: > RSS_OpeningStyle.OpeningStyleCode, name: 'FK_RSS_OpeningTemplate_RSS_OpeningStyle']
  HasWeatherStrip          boolean        [null]
  HasInsectScreen          boolean        [null]
  GlassDataJson            varchar        [null]
  PerformanceJson          varchar        [null]
  ShaftReflectance         decimal(15,5)  [null]
  ShaftWallRValue          decimal(15,5)  [null]
  ShaftLength              decimal(15,5)  [null]
  HasInteriorShades        boolean        [null]
  ExteriorShadeFactor      decimal(15,5)  [null]
  HasDiffuserShades        boolean        [null]
  VisualisationDataJson    varchar        [null]
  FrameSolarAbsorptance    decimal(15,5)  [null]
  AllowEditingFrameSolarAbsorptance boolean [null]
  FrameColourId            UUID           [null, ref: > RSS_Colour.ColourId, name: 'FK_RSS_OpeningTemplate_RSS_Colour_FrameColourId']
  AllowEditingFrameColour  boolean        [null]
  NccOpeningStyleCode      varchar(20)    [null, ref: > RSS_NccOpeningStyle.OpeningStyleCode, name: 'FK_RSS_openingStyle_RSS_NccOpeningStyle_OpeningStyleCode']
  Azimuth                  decimal(15,5)  [null]
}
// INDEXES
//      - IX_RSS_OpeningTemplate_Category (ConstructionCategoryCode)


Table RSS_SurfaceTemplate {
  ConstructionId                        UUID           [not null, PK]
  Deleted                               boolean        [not null]
  CreatedOn                             timestamp      [not null]
  CreatedByName                         varchar(50)    [null]
  ModifiedOn                            timestamp      [null]
  ModifiedByName                        varchar(50)    [null]
  Description                           varchar(100)   [null]
  DisplayDescription                    varchar(100)   [null]
  ManufacturerId                        UUID           [null, ref: > RSS_Manufacturer.ManufacturerId, name: 'FK_RSS_SurfaceTemplate_RSS_Manufacturer']
  ExternalConstructionId                varchar(100)   [null]
  AdjacencyCode                         varchar(20)    [null, ref: > RSS_Adjacency.AdjacencyCode, name: 'FK_RSS_SurfaceTemplate_RSS_Adjacency']
  UnitOfMeasureCode                     varchar(20)    [null, ref: > RSS_UnitOfMeasure.UnitOfMeasureCode, name: 'FK_RSS_SurfaceTemplate_RSS_UnitOfMeasure']
  Comments                              varchar        [null]
  LifeCycleDataJson                     varchar        [null]
  ChenathDataJson                       varchar        [null]
  FR5DataJson                           varchar        [null]
  HeroDataJson                          varchar        [null]
  EPDataJson                            varchar        [null]
  ConstructionCategoryCode              varchar(20)    [null, ref: > RSS_ConstructionCategory.ConstructionCategoryCode, name: 'FK_RSS_SurfaceTemplate_RSS_ConstructionCategory']
  AirCavityCode                         varchar(20)    [null, ref: > RSS_AirCavity.AirCavityCode, name: 'FK_RSS_SurfaceTemplate_RSS_AirCavity']
  Thickness                             decimal(15,5)  [null]
  Density                               decimal(15,5)  [null]
  FloorCovering                         varchar(100)   [null]
  Tilt                                  decimal(15,5)  [null]
  ExteriorSolarAbsorptance              decimal(15,5)  [null]
  InteriorSolarAbsorptance              decimal(15,5)  [null]
  SystemRValue                          decimal(15,5)  [null]
  OpeningStyleCode                      varchar(20)    [null]
  Openability                           varchar(100)   [null]
  HasWeatherStrip                       boolean        [null]
  HasInsectScreen                       boolean        [null]
  InsulationDataJson                    varchar        [null]
  VisualisationDataJson                 varchar        [null]
  AllowEditingExteriorSolarAbsorptance  boolean        [null]
  AllowEditingInteriorSolarAbsorptance  boolean        [null]
  ExteriorColourId                      UUID           [null, ref: > RSS_Colour.ColourId, name: 'FK_RSS_SurfaceTemplate_RSS_Colour_ExteriorColourId']
  InteriorColourId                      UUID           [null, ref: > RSS_Colour.ColourId, name: 'FK_RSS_SurfaceTemplate_RSS_Colour_InteriorColourId']
  AllowEditingExteriorColour            boolean        [null]
  AllowEditingInteriorColour            boolean        [null]
  NccOpeningStyleCode                   varchar(20)    [null, ref: > RSS_NccOpeningStyle.OpeningStyleCode, name: 'FK_RSS_SurfaceTemplate_RSS_NccOpeningStyle_OpeningStyleCode']
}
// INDEXES
//      - IX_RSS_SurfaceTemplate_Category (ConstructionCategoryCode)


// - Service and Zone Tables - //

Table RSS_ServiceCategory {
  ServiceCategoryCode varchar(20)  [not null, PK]
  Description         varchar(200) [null]
  SortOrder           smallint     [not null]
  Deleted             boolean      [not null]
  MeasurementType     varchar(50)  [null]
}


Table RSS_ServiceType {
  ServiceTypeCode     varchar(20)  [not null, PK]
  ServiceCategoryCode varchar(20)  [not null, ref: > RSS_ServiceCategory.ServiceCategoryCode, name: 'FK_RSS_ServiceType_RSS_ServiceCategory']
  Description         varchar(200) [null]
  SortOrder           smallint     [not null]
  Deleted             boolean      [not null]
}


Table RSS_ServiceBatteryType {
  ServiceBatteryTypeCode varchar(20)  [not null, PK]
  Description            varchar(100) [null]
  Deleted                boolean      [not null, default: FALSE]
}


Table RSS_ServiceControlDevice {
  ServiceControlDeviceCode varchar(20)  [not null, PK]
  Description              varchar(100) [null]
  Deleted                  boolean      [not null, default: FALSE]
}


Table RSS_ServiceFuelType {
  ServiceFuelTypeCode varchar(20)  [not null, PK]
  Description         varchar(100) [null]
  Deleted             boolean      [not null, default: FALSE]
}


Table RSS_ServicePumpType {
  ServicePumpTypeCode varchar(20)  [not null, PK]
  Description         varchar(100) [null]
  Deleted             boolean      [not null, default: FALSE]
}


Table ServiceType {
  ServiceTypeCode varchar(20)  [not null, PK]
  Description     varchar(100) [null]
  Deleted         boolean      [not null, default: FALSE]
}


Table HeatingSystemType {
  HeatingSystemTypeCode varchar(20)  [not null, PK]
  Description           varchar(100) [null]
  Deleted               boolean      [not null, default: FALSE]
}


Table ICRating {
  ICRatingCode varchar(20)  [not null, PK]
  Description  varchar(100) [null]
  Deleted      boolean      [not null, default: FALSE]
}


Table RSS_ServiceTemplate {
  ServiceTemplateId   UUID         [not null, PK]
  ServiceCategoryCode varchar(20)  [not null, ref: > RSS_ServiceCategory.ServiceCategoryCode, name: 'FK_RSS_ServiceTemplate_RSS_ServiceCategory']
  ServiceTypeCode     varchar(20)  [not null, ref: > RSS_ServiceType.ServiceTypeCode, name: 'FK_RSS_ServiceTemplate_RSS_ServiceType']
  ServiceTypeCode2    varchar(20)  [null, ref: > ServiceType.ServiceTypeCode, name: 'FK_RSS_ServiceTemplate_ServiceType_ServiceTypeCode']
  Description         varchar(100) [null]
  DisplayDescription  varchar(100) [null]
  ManufacturerId      UUID         [null, ref: > RSS_Manufacturer.ManufacturerId, name: 'FK_RSS_ServiceTemplate_RSS_Manufacturer']
  SupplierId          UUID         [null, ref: > RSS_Supplier.SupplierId, name: 'FK_RSS_ServiceTemplate_RSS_Supplier_Id']
  ExternalServiceId   varchar(100) [null]
  UnitOfMeasureCode   varchar(20)  [null, ref: > RSS_UnitOfMeasure.UnitOfMeasureCode, name: 'FK_RSS_ServiceTemplate_RSS_UnitOfMeasure']
  ServiceBatteryTypeCode varchar(20) [null, ref: > RSS_ServiceBatteryType.ServiceBatteryTypeCode, name: 'FK_RSS_ServiceTemplate_RSS_ServiceBatteryType']
  ServiceControlDeviceCode varchar(20) [null, ref: > RSS_ServiceControlDevice.ServiceControlDeviceCode, name: 'FK_RSS_ServiceTemplate_RSS_ServiceControlDevice_ServiceControlDeviceCode']
  ServiceFuelTypeCode varchar(20) [null, ref: > RSS_ServiceFuelType.ServiceFuelTypeCode, name: 'FK_RSS_ServiceTemplate_RSS_ServiceFuelType_ServiceFuelTypeCode']
  ServicePumpTypeCode varchar(20) [null, ref: > RSS_ServicePumpType.ServicePumpTypeCode, name: 'FK_RSS_ServiceTemplate_RSS_ServicePumpType_ServicePumpTypeCode']
  HeatingSystemTypeCode varchar(20) [null, ref: > HeatingSystemType.HeatingSystemTypeCode, name: 'FK_ServiceTemplate_HeatingSystemType_HeatingSystemTypeCode']
  ICRatingCode        varchar(20) [null, ref: > ICRating.ICRatingCode, name: 'FK_ServiceTemplate_ICRating_ICRatingCode']
  Comments            varchar      [null]
  LifeCycleDataJson   varchar      [null]
  ChenathDataJson     varchar      [null]
  FR5DataJson         varchar      [null]
  HeroDataJson        varchar      [null]
  EPDataJson          varchar      [null]
  PerformanceJson     varchar      [null]
  VisualisationDataJson varchar    [null]
  CreatedOn           timestamp    [not null]
  CreatedByName       varchar(50)  [null]
  ModifiedOn          timestamp    [null]
  ModifiedByName      varchar(50)  [null]
  Deleted             boolean      [not null]
}
// INDEXES
//      - IX_RSS_ServiceTemplate_Category (ServiceCategoryCode)


Table RSS_ZoneType {
  ZoneTypeCode                    varchar(20)  [not null, PK]
  Description                     varchar(100) [null]
  DefaultNCCClassificationCode    varchar(20)  [null, ref: > RSS_NCCClassification.NCCClassificationCode, name: 'FK_RSS_ZoneType_RSS_NCCClassification_DefaultNCCClassificationCode']
  SortOrder                       smallint     [not null]
  Deleted                         boolean      [not null]
  AvailableFor                    varchar(50)  [null]
  CreatedOn                       timestamp    [not null]
  CreatedByName                   varchar(50)  [null]
  ModifiedOn                      timestamp    [null]
  ModifiedByName                  varchar(50)  [null]
}


Table RSS_NCCClassification {
  NCCClassificationCode varchar(20)  [not null, PK]
  Description           varchar(100) [null]
  Deleted               boolean      [not null, default: FALSE]
}


Table RSS_ZoneActivity {
  ZoneActivityCode varchar(20)  [not null, PK]
  Description      varchar(100) [null]
  Deleted          boolean      [not null, default: FALSE]
}


Table SpaceTypeCode {
  SpaceTypeCode varchar(20)  [not null, PK]
  Description   varchar(100) [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_Zone {
  ZoneId                           UUID         [not null, PK]
  AssessmentComplianceBuildingId   UUID         [not null, ref: > RSS_AssessmentComplianceBuilding.AssessmentComplianceBuildingId, name: 'FK_RSS_Zone_RSS_AssessmentComplianceBuilding']
  ZoneTypeCode                     varchar(20)  [not null, ref: > RSS_ZoneType.ZoneTypeCode, name: 'FK_RSS_Zone_RSS_ZoneType']
  AirCavityCode                    varchar(20)  [null, ref: > RSS_AirCavity.AirCavityCode, name: 'FK_RSS_Zone_RSS_AirCavity_AirCavityCode']
  NCCClassificationCode            varchar(20)  [null, ref: > RSS_NCCClassification.NCCClassificationCode, name: 'FK_RSS_Zone_RSS_NCCClassification_NCCClassificationCode']
  ZoneActivityCode                 varchar(20)  [null, ref: > RSS_ZoneActivity.ZoneActivityCode, name: 'FK_RSS_Zone_RSS_ZoneActivity_ZoneActivityCode']
  SpaceTypeCode                    varchar(20)  [null, ref: > SpaceTypeCode.SpaceTypeCode, name: 'FK_Zone_SpaceTypeCode']
  Description                      varchar(100) [null]
  FloorArea                        decimal(15,5) [null]
  Volume                           decimal(15,5) [null]
  CreatedOn                        timestamp    [not null]
  CreatedByName                    varchar(50)  [null]
  ModifiedOn                       timestamp    [null]
  ModifiedByName                   varchar(50)  [null]
  Deleted                          boolean      [not null]
}
// INDEXES
//      - IX_Zone_AssessmentComplianceBuilding (AssessmentComplianceBuildingId)


Table RSS_UnitOfMeasure {
  UnitOfMeasureCode varchar(20)  [not null, PK]
  Description       varchar(200) [null]
  MeasurementType   varchar(50)  [null]
  SortOrder         smallint     [not null]
  Deleted           boolean      [not null]
}


Table RSS_Adjacency {
  AdjacencyCode varchar(20)  [not null, PK]
  Description   varchar(100) [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_FrameMaterial {
  FrameMaterialCode varchar(20)  [not null, PK]
  Description       varchar(100) [null]
  Deleted           boolean      [not null, default: FALSE]
}


Table RSS_OpeningStyle {
  OpeningStyleCode varchar(20)  [not null, PK]
  Description      varchar(100) [null]
  Deleted          boolean      [not null, default: FALSE]
}


Table RSS_NccOpeningStyle {
  OpeningStyleCode varchar(20)  [not null, PK]
  Description      varchar(100) [null]
  Deleted          boolean      [not null, default: FALSE]
}


Table RSS_AirCavity {
  AirCavityCode varchar(20)  [not null, PK]
  Description   varchar(100) [null]
  Deleted       boolean      [not null, default: FALSE]
}


Table RSS_Opening {
  OpeningId                UUID         [not null, PK]
  Description              varchar(100) [null]
  AdjacencyCode            varchar(20)  [null, ref: > RSS_Adjacency.AdjacencyCode, name: 'FK_RSS_Opening_RSS_Adjacency_Code']
  ConstructionCategoryCode varchar(20)  [null, ref: > RSS_ConstructionCategory.ConstructionCategoryCode, name: 'FK_RSS_Opening_RSS_ConstructionCategory_Code']
  ConstructionSubCategoryCode varchar(20) [null, ref: > RSS_ConstructionSubCategory.ConstructionSubCategoryCode, name: 'FK_RSS_Opening_RSS_ConstructionSubCategory_Code']
  FrameMaterialCode        varchar(20)  [null, ref: > RSS_FrameMaterial.FrameMaterialCode, name: 'FK_RSS_Opening_RSS_FrameMaterial']
  OpeningStyleCode         varchar(20)  [null, ref: > RSS_OpeningStyle.OpeningStyleCode, name: 'FK_RSS_Opening_RSS_OpeningStylea']
  SupplierId               UUID         [null, ref: > RSS_Supplier.SupplierId, name: 'FK_RSS_Opening_RSS_Supplier_Id']
  UnitOfMeasureCode        varchar(20)  [null, ref: > RSS_UnitOfMeasure.UnitOfMeasureCode, name: 'FK_RSS_Opening_RSS_UnitOfMeasure']
  Deleted                  boolean      [not null, default: FALSE]
}


Table RSS_Surface {
  SurfaceId                UUID         [not null, PK]
  Description              varchar(100) [null]
  AdjacencyCode            varchar(20)  [null, ref: > RSS_Adjacency.AdjacencyCode, name: 'FK_RSS_Surface_RSS_Adjacency_Code']
  AirCavityCode            varchar(20)  [null, ref: > RSS_AirCavity.AirCavityCode, name: 'FK_RSS_Surface_RSS_AirCavity']
  ConstructionCategoryCode varchar(20)  [null, ref: > RSS_ConstructionCategory.ConstructionCategoryCode, name: 'FK_RSS_Surface_RSS_ConstructionCategory_Code']
  ConstructionSubCategoryCode varchar(20) [null, ref: > RSS_ConstructionSubCategory.ConstructionSubCategoryCode, name: 'FK_RSS_Surface_RSS_ConstructionSubCategory_Code']
  OpeningStyleCode         varchar(20)  [null, ref: > RSS_OpeningStyle.OpeningStyleCode, name: 'FK_RSS_Surface_RSS_OpeningStyle']
  SupplierId               UUID         [null, ref: > RSS_Supplier.SupplierId, name: 'FK_RSS_Surface_RSS_Supplier_Id']
  UnitOfMeasureCode        varchar(20)  [null, ref: > RSS_UnitOfMeasure.UnitOfMeasureCode, name: 'FK_RSS_Surface_RSS_UnitOfMeasure']
  Deleted                  boolean      [not null, default: FALSE]
}


Table RSS_ConstructionSubCategory {
  ConstructionSubCategoryCode varchar(20)  [not null, PK]
  Description                 varchar(100) [null]
  Deleted                     boolean      [not null, default: FALSE]
}



