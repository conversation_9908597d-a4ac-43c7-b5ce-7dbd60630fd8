-- Clear Database to Initial State Script
-- Clears Assessments and Jobs only, preserves Users/Auth, Clients, and Projects
-- Runs in transaction - rolls back if any part fails

SET NOCOUNT ON
SET XACT_ABORT ON

BEGIN TRANSACTION ClearDbToInitialState

BEGIN TRY
    -- Drop constraints that cannot be resolved by reordering
    PRINT 'Dropping foreign key constraints...'

    -- Drop File -> AssessmentComplianceOption constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_File] DROP CONSTRAINT [FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId]
            PRINT 'Successfully dropped: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'

    -- Drop Job <-> Assessment circular dependency constraints
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_Job_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_Job_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_Job] DROP CONSTRAINT [FK_RSS_Job_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_Job_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_Job_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_Job_RSS_Assessment'

    -- Drop AssessmentComplianceOption <-> AssessmentComplianceBuilding circular dependency constraints
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_ACO_ReferenceBuilding')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_ACO_ReferenceBuilding'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] DROP CONSTRAINT [FK_RSS_ACO_ReferenceBuilding]
            PRINT 'Successfully dropped: FK_RSS_ACO_ReferenceBuilding'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_ACO_ReferenceBuilding: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_ACO_ReferenceBuilding'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_ACO_ProposedBuilding')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_ACO_ProposedBuilding'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] DROP CONSTRAINT [FK_RSS_ACO_ProposedBuilding]
            PRINT 'Successfully dropped: FK_RSS_ACO_ProposedBuilding'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_ACO_ProposedBuilding: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_ACO_ProposedBuilding'

    -- Drop Zone -> AssessmentComplianceBuilding constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_AssCompBuild_ID')
    BEGIN
        PRINT 'Dropping constraint: FK_AssCompBuild_ID'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_Zone] DROP CONSTRAINT [FK_AssCompBuild_ID]
            PRINT 'Successfully dropped: FK_AssCompBuild_ID'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_AssCompBuild_ID: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_AssCompBuild_ID'

    -- Drop AssessmentComplianceBuilding -> AssessmentComplianceOption constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_ASSCOMPSIM_ID')
    BEGIN
        PRINT 'Dropping constraint: FK_ASSCOMPSIM_ID'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentComplianceBuilding] DROP CONSTRAINT [FK_ASSCOMPSIM_ID]
            PRINT 'Successfully dropped: FK_ASSCOMPSIM_ID'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_ASSCOMPSIM_ID: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_ASSCOMPSIM_ID'

    -- Drop AssessmentDrawing -> AssessmentComplianceOption constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_ComplianceOption_ID')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_ComplianceOption_ID'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentDrawing] DROP CONSTRAINT [FK_RSS_ComplianceOption_ID]
            PRINT 'Successfully dropped: FK_RSS_ComplianceOption_ID'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_ComplianceOption_ID: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_ComplianceOption_ID'

    -- Drop Assessment self-reference constraint (template relationship)
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_Assessment_Template')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_Assessment_Template'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_Assessment] DROP CONSTRAINT [FK_RSS_Assessment_Template]
            PRINT 'Successfully dropped: FK_RSS_Assessment_Template'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_Assessment_Template: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_Assessment_Template'

    -- Drop NotificationDispatchCheckRequest -> Assessment constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_NotificationDispatchCheckRequest] DROP CONSTRAINT [FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId]
            PRINT 'Successfully dropped: FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId'

    -- Drop File -> Assessment constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_File_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_File_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_File] DROP CONSTRAINT [FK_RSS_File_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_File_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_File_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_File_RSS_Assessment'

    -- Drop ClientAssessmentTemplate -> Assessment constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_Assessment_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_Assessment_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_ClientAssessmentTemplate] DROP CONSTRAINT [FK_RSS_Assessment_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_Assessment_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_Assessment_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_Assessment_RSS_Assessment'

    -- Drop assessment-related table constraints
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentConstructionItem_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentConstructionItem_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentConstructionItem] DROP CONSTRAINT [FK_RSS_AssessmentConstructionItem_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentConstructionItem_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentConstructionItem_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentConstructionItem_RSS_Assessment'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentExternalGlazing_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentExternalGlazing_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentExternalGlazing] DROP CONSTRAINT [FK_RSS_AssessmentExternalGlazing_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentExternalGlazing_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentExternalGlazing_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentExternalGlazing_RSS_Assessment'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentRoofLight_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentRoofLight_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentRoofLight] DROP CONSTRAINT [FK_RSS_AssessmentRoofLight_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentRoofLight_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentRoofLight_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentRoofLight_RSS_Assessment'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentArtificialLighting_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentArtificialLighting_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentArtificialLighting] DROP CONSTRAINT [FK_RSS_AssessmentArtificialLighting_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentArtificialLighting_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentArtificialLighting_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentArtificialLighting_RSS_Assessment'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentNote_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentNote_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentNote] DROP CONSTRAINT [FK_RSS_AssessmentNote_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentNote_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentNote_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentNote_RSS_Assessment'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentReasonForResults_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentReasonForResults_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentReasonForResults] DROP CONSTRAINT [FK_RSS_AssessmentReasonForResults_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentReasonForResults_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentReasonForResults_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentReasonForResults_RSS_Assessment'

    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_ClientSystemTemplate_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_ClientSystemTemplate_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_SystemTemplate] DROP CONSTRAINT [FK_RSS_ClientSystemTemplate_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_ClientSystemTemplate_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_ClientSystemTemplate_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_ClientSystemTemplate_RSS_Assessment'

    -- Drop AssessmentComplianceOption -> Assessment constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentComplianceOption_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentComplianceOption_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] DROP CONSTRAINT [FK_RSS_AssessmentComplianceOption_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentComplianceOption_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentComplianceOption_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentComplianceOption_RSS_Assessment'

    -- Drop AssessmentProjectDetail -> Assessment constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentProjectDetail_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentProjectDetail_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] DROP CONSTRAINT [FK_RSS_AssessmentProjectDetail_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentProjectDetail_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentProjectDetail_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentProjectDetail_RSS_Assessment'

    -- Drop AssessmentDrawing -> Assessment constraint
    IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_RSS_AssessmentDrawing_RSS_Assessment')
    BEGIN
        PRINT 'Dropping constraint: FK_RSS_AssessmentDrawing_RSS_Assessment'
        BEGIN TRY
            ALTER TABLE [dbo].[RSS_AssessmentDrawing] DROP CONSTRAINT [FK_RSS_AssessmentDrawing_RSS_Assessment]
            PRINT 'Successfully dropped: FK_RSS_AssessmentDrawing_RSS_Assessment'
        END TRY
        BEGIN CATCH
            PRINT 'ERROR dropping FK_RSS_AssessmentDrawing_RSS_Assessment: ' + ERROR_MESSAGE()
            PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS NVARCHAR(10)) + ', Line: ' + CAST(ERROR_LINE() AS NVARCHAR(10))
        END CATCH
    END
    ELSE
        PRINT 'Constraint not found: FK_RSS_AssessmentDrawing_RSS_Assessment'

    -- Clear tables in proper dependency order (deepest children first)
    PRINT 'Clearing data from tables in dependency order...'

    -- 1. Clear deepest child tables first (no dependencies on other tables being cleared)
    TRUNCATE TABLE [dbo].[RSS_Zone]
    PRINT 'Cleared RSS_Zone'

    -- Clear NotificationDispatchCheckRequest (depends on Assessment)
    IF OBJECT_ID('[dbo].[RSS_NotificationDispatchCheckRequest]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_NotificationDispatchCheckRequest]
        PRINT 'Cleared RSS_NotificationDispatchCheckRequest'
    END

    -- Clear all assessment-related tables (depend on Assessment)
    IF OBJECT_ID('[dbo].[RSS_AssessmentConstructionItem]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_AssessmentConstructionItem]
        PRINT 'Cleared RSS_AssessmentConstructionItem'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentExternalGlazing]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_AssessmentExternalGlazing]
        PRINT 'Cleared RSS_AssessmentExternalGlazing'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentRoofLight]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_AssessmentRoofLight]
        PRINT 'Cleared RSS_AssessmentRoofLight'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentArtificialLighting]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_AssessmentArtificialLighting]
        PRINT 'Cleared RSS_AssessmentArtificialLighting'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentNote]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_AssessmentNote]
        PRINT 'Cleared RSS_AssessmentNote'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentReasonForResults]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_AssessmentReasonForResults]
        PRINT 'Cleared RSS_AssessmentReasonForResults'
    END

    IF OBJECT_ID('[dbo].[RSS_ClientAssessmentTemplate]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_ClientAssessmentTemplate]
        PRINT 'Cleared RSS_ClientAssessmentTemplate'
    END

    IF OBJECT_ID('[dbo].[RSS_SystemTemplate]', 'U') IS NOT NULL
    BEGIN
        TRUNCATE TABLE [dbo].[RSS_SystemTemplate]
        PRINT 'Cleared RSS_SystemTemplate'
    END

    -- 2. Clear tables that depend on AssessmentComplianceOption but not on AssessmentComplianceBuilding
    TRUNCATE TABLE [dbo].[RSS_AssessmentDrawing]
    PRINT 'Cleared RSS_AssessmentDrawing'

    -- 3. Clear AssessmentComplianceBuilding (depends on AssessmentComplianceOption)
    TRUNCATE TABLE [dbo].[RSS_AssessmentComplianceBuilding]
    PRINT 'Cleared RSS_AssessmentComplianceBuilding'

    -- 4. Clear AssessmentComplianceOption (now safe after AssessmentComplianceBuilding is cleared)
    TRUNCATE TABLE [dbo].[RSS_AssessmentComplianceOption]
    PRINT 'Cleared RSS_AssessmentComplianceOption'

    -- 5. Clear other assessment-related tables
    TRUNCATE TABLE [dbo].[RSS_AssessmentProjectDetail]
    PRINT 'Cleared RSS_AssessmentProjectDetail'

    -- 6. Clear job-related tables
    TRUNCATE TABLE [dbo].[RSS_JobEvent]
    PRINT 'Cleared RSS_JobEvent'

    -- 7. Clear Assessment (depends on Job, but Job.CurrentAssessmentId constraint is dropped)
    TRUNCATE TABLE [dbo].[RSS_Assessment]
    PRINT 'Cleared RSS_Assessment'

    -- 8. Clear Job last (now safe after Assessment is cleared)
    TRUNCATE TABLE [dbo].[RSS_Job]
    PRINT 'Cleared RSS_Job'

    IF OBJECT_ID('[dbo].[RSS_AssessmentSoftwareComplianceMethod]', 'U') IS NOT NULL
        TRUNCATE TABLE [dbo].[RSS_AssessmentSoftwareComplianceMethod]

    -- Clear file-related data (preserve files linked to projects)
    DELETE FROM [dbo].[RSS_FileVersion] WHERE [FileId] IN (
        SELECT [FileId] FROM [dbo].[RSS_File]
        WHERE ([JobId] IS NOT NULL OR [AssessmentId] IS NOT NULL)
           OR ([JobId] IS NULL AND [AssessmentId] IS NULL AND [FileId] NOT IN (
               SELECT DISTINCT [LogoFileId] FROM [dbo].[RSS_Project] WHERE [LogoFileId] IS NOT NULL
           ))
    )
    DELETE FROM [dbo].[RSS_File] WHERE ([JobId] IS NOT NULL OR [AssessmentId] IS NOT NULL)
       OR ([JobId] IS NULL AND [AssessmentId] IS NULL AND [FileId] NOT IN (
           SELECT DISTINCT [LogoFileId] FROM [dbo].[RSS_Project] WHERE [LogoFileId] IS NOT NULL
       ))

    -- Clear template and reference data
    TRUNCATE TABLE [dbo].[RSS_ServiceTemplate]
    TRUNCATE TABLE [dbo].[RSS_OpeningTemplate]
    TRUNCATE TABLE [dbo].[RSS_SurfaceTemplate]

    -- Clear reference data
    TRUNCATE TABLE [dbo].[RSS_Colour]
    TRUNCATE TABLE [dbo].[RSS_Manufacturer]

    -- Recreate the constraints that were dropped
    PRINT 'Recreating foreign key constraints...'

    -- Recreate File -> AssessmentComplianceOption constraint
    ALTER TABLE [dbo].[RSS_File] WITH CHECK ADD CONSTRAINT [FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId]
        FOREIGN KEY([ComplianceOptionId]) REFERENCES [dbo].[RSS_AssessmentComplianceOption]([ComplianceOptionsId])
    ALTER TABLE [dbo].[RSS_File] CHECK CONSTRAINT [FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId]
    PRINT 'Recreated: FK_RSS_File_RSS_AssessmentComplianceOption_ComplianceOptionId'

    -- Recreate Job <-> Assessment circular dependency constraint
    ALTER TABLE [dbo].[RSS_Job] WITH CHECK ADD CONSTRAINT [FK_RSS_Job_RSS_Assessment]
        FOREIGN KEY([CurrentAssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
    ALTER TABLE [dbo].[RSS_Job] CHECK CONSTRAINT [FK_RSS_Job_RSS_Assessment]
    PRINT 'Recreated: FK_RSS_Job_RSS_Assessment'

    -- Recreate AssessmentComplianceOption <-> AssessmentComplianceBuilding circular dependency constraints
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] WITH CHECK ADD CONSTRAINT [FK_RSS_ACO_ReferenceBuilding]
        FOREIGN KEY([ReferenceBuildingId]) REFERENCES [dbo].[RSS_AssessmentComplianceBuilding]([AssessmentComplianceBuildingId])
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] CHECK CONSTRAINT [FK_RSS_ACO_ReferenceBuilding]
    PRINT 'Recreated: FK_RSS_ACO_ReferenceBuilding'

    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] WITH CHECK ADD CONSTRAINT [FK_RSS_ACO_ProposedBuilding]
        FOREIGN KEY([ProposedBuildingId]) REFERENCES [dbo].[RSS_AssessmentComplianceBuilding]([AssessmentComplianceBuildingId])
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] CHECK CONSTRAINT [FK_RSS_ACO_ProposedBuilding]
    PRINT 'Recreated: FK_RSS_ACO_ProposedBuilding'

    -- Recreate Zone -> AssessmentComplianceBuilding constraint
    ALTER TABLE [dbo].[RSS_Zone] WITH CHECK ADD CONSTRAINT [FK_AssCompBuild_ID]
        FOREIGN KEY([AssessmentComplianceBuildingId]) REFERENCES [dbo].[RSS_AssessmentComplianceBuilding]([AssessmentComplianceBuildingId])
    ALTER TABLE [dbo].[RSS_Zone] CHECK CONSTRAINT [FK_AssCompBuild_ID]
    PRINT 'Recreated: FK_AssCompBuild_ID'

    -- Recreate AssessmentComplianceBuilding -> AssessmentComplianceOption constraint
    ALTER TABLE [dbo].[RSS_AssessmentComplianceBuilding] WITH CHECK ADD CONSTRAINT [FK_ASSCOMPSIM_ID]
        FOREIGN KEY([AssessmentComplianceOptionId]) REFERENCES [dbo].[RSS_AssessmentComplianceOption]([ComplianceOptionsId])
    ALTER TABLE [dbo].[RSS_AssessmentComplianceBuilding] CHECK CONSTRAINT [FK_ASSCOMPSIM_ID]
    PRINT 'Recreated: FK_ASSCOMPSIM_ID'

    -- Recreate AssessmentDrawing -> AssessmentComplianceOption constraint
    ALTER TABLE [dbo].[RSS_AssessmentDrawing] WITH CHECK ADD CONSTRAINT [FK_RSS_ComplianceOption_ID]
        FOREIGN KEY([ComplianceOptionId]) REFERENCES [dbo].[RSS_AssessmentComplianceOption]([ComplianceOptionsId])
    ALTER TABLE [dbo].[RSS_AssessmentDrawing] CHECK CONSTRAINT [FK_RSS_ComplianceOption_ID]
    PRINT 'Recreated: FK_RSS_ComplianceOption_ID'

    -- Recreate Assessment self-reference constraint (template relationship)
    ALTER TABLE [dbo].[RSS_Assessment] WITH CHECK ADD CONSTRAINT [FK_RSS_Assessment_Template]
        FOREIGN KEY([AssessmentTemplateId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
    ALTER TABLE [dbo].[RSS_Assessment] CHECK CONSTRAINT [FK_RSS_Assessment_Template]
    PRINT 'Recreated: FK_RSS_Assessment_Template'

    -- Recreate NotificationDispatchCheckRequest -> Assessment constraint
    IF OBJECT_ID('[dbo].[RSS_NotificationDispatchCheckRequest]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_NotificationDispatchCheckRequest] WITH CHECK ADD CONSTRAINT [FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
        ALTER TABLE [dbo].[RSS_NotificationDispatchCheckRequest] CHECK CONSTRAINT [FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId]
        PRINT 'Recreated: FK_RSS_RSS_NotificationDispatchCheckRequest_RSS_Assessment_AssessmentId'
    END

    -- Recreate File -> Assessment constraint
    ALTER TABLE [dbo].[RSS_File] WITH CHECK ADD CONSTRAINT [FK_RSS_File_RSS_Assessment]
        FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
    ALTER TABLE [dbo].[RSS_File] CHECK CONSTRAINT [FK_RSS_File_RSS_Assessment]
    PRINT 'Recreated: FK_RSS_File_RSS_Assessment'

    -- Recreate ClientAssessmentTemplate -> Assessment constraint
    IF OBJECT_ID('[dbo].[RSS_ClientAssessmentTemplate]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_ClientAssessmentTemplate] WITH CHECK ADD CONSTRAINT [FK_RSS_Assessment_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
        ALTER TABLE [dbo].[RSS_ClientAssessmentTemplate] CHECK CONSTRAINT [FK_RSS_Assessment_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_Assessment_RSS_Assessment'
    END

    -- Recreate assessment-related table constraints
    IF OBJECT_ID('[dbo].[RSS_AssessmentConstructionItem]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_AssessmentConstructionItem] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentConstructionItem_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
        ALTER TABLE [dbo].[RSS_AssessmentConstructionItem] CHECK CONSTRAINT [FK_RSS_AssessmentConstructionItem_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_AssessmentConstructionItem_RSS_Assessment'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentExternalGlazing]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_AssessmentExternalGlazing] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentExternalGlazing_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
        ALTER TABLE [dbo].[RSS_AssessmentExternalGlazing] CHECK CONSTRAINT [FK_RSS_AssessmentExternalGlazing_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_AssessmentExternalGlazing_RSS_Assessment'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentRoofLight]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_AssessmentRoofLight] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentRoofLight_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
        ALTER TABLE [dbo].[RSS_AssessmentRoofLight] CHECK CONSTRAINT [FK_RSS_AssessmentRoofLight_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_AssessmentRoofLight_RSS_Assessment'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentArtificialLighting]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_AssessmentArtificialLighting] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentArtificialLighting_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
        ALTER TABLE [dbo].[RSS_AssessmentArtificialLighting] CHECK CONSTRAINT [FK_RSS_AssessmentArtificialLighting_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_AssessmentArtificialLighting_RSS_Assessment'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentNote]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_AssessmentNote] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentNote_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
        ALTER TABLE [dbo].[RSS_AssessmentNote] CHECK CONSTRAINT [FK_RSS_AssessmentNote_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_AssessmentNote_RSS_Assessment'
    END

    IF OBJECT_ID('[dbo].[RSS_AssessmentReasonForResults]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_AssessmentReasonForResults] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentReasonForResults_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
        ALTER TABLE [dbo].[RSS_AssessmentReasonForResults] CHECK CONSTRAINT [FK_RSS_AssessmentReasonForResults_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_AssessmentReasonForResults_RSS_Assessment'
    END

    IF OBJECT_ID('[dbo].[RSS_SystemTemplate]', 'U') IS NOT NULL
    BEGIN
        ALTER TABLE [dbo].[RSS_SystemTemplate] WITH CHECK ADD CONSTRAINT [FK_RSS_ClientSystemTemplate_RSS_Assessment]
            FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
        ALTER TABLE [dbo].[RSS_SystemTemplate] CHECK CONSTRAINT [FK_RSS_ClientSystemTemplate_RSS_Assessment]
        PRINT 'Recreated: FK_RSS_ClientSystemTemplate_RSS_Assessment'
    END

    -- Recreate AssessmentComplianceOption -> Assessment constraint
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentComplianceOption_RSS_Assessment]
        FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
    ALTER TABLE [dbo].[RSS_AssessmentComplianceOption] CHECK CONSTRAINT [FK_RSS_AssessmentComplianceOption_RSS_Assessment]
    PRINT 'Recreated: FK_RSS_AssessmentComplianceOption_RSS_Assessment'

    -- Recreate AssessmentProjectDetail -> Assessment constraint
    ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentProjectDetail_RSS_Assessment]
        FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId])
    ALTER TABLE [dbo].[RSS_AssessmentProjectDetail] CHECK CONSTRAINT [FK_RSS_AssessmentProjectDetail_RSS_Assessment]
    PRINT 'Recreated: FK_RSS_AssessmentProjectDetail_RSS_Assessment'

    -- Recreate AssessmentDrawing -> Assessment constraint
    ALTER TABLE [dbo].[RSS_AssessmentDrawing] WITH CHECK ADD CONSTRAINT [FK_RSS_AssessmentDrawing_RSS_Assessment]
        FOREIGN KEY([AssessmentId]) REFERENCES [dbo].[RSS_Assessment]([AssessmentId]) ON DELETE CASCADE
    ALTER TABLE [dbo].[RSS_AssessmentDrawing] CHECK CONSTRAINT [FK_RSS_AssessmentDrawing_RSS_Assessment]
    PRINT 'Recreated: FK_RSS_AssessmentDrawing_RSS_Assessment'

    -- Give "<EMAIL>" all roles
    INSERT INTO [dbo].[AspNetUserRoles]
    ([UserId], [RoleId])
    SELECT 'C65BA6BD-2D80-4BEE-B213-6ACFA0016C15', [role].[Id]
    FROM [dbo].[AspNetRoles] [role]
    WHERE NOT EXISTS (
        SELECT 1
        FROM [dbo].[AspNetUserRoles] [userRole]
        WHERE [userRole].[UserId] = 'C65BA6BD-2D80-4BEE-B213-6ACFA0016C15'
          AND [userRole].[RoleId] = [role].[Id]
    );



    COMMIT TRANSACTION ClearDbToInitialState
    PRINT 'Database cleanup completed - Assessment and job data cleared.'

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION ClearDbToInitialState

    DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
    DECLARE @ErrorState INT = ERROR_STATE()
    DECLARE @ErrorNumber INT = ERROR_NUMBER()
    DECLARE @ErrorProcedure NVARCHAR(128) = ERROR_PROCEDURE()
    DECLARE @ErrorLine INT = ERROR_LINE()

    PRINT 'ERROR: Database cleanup failed and rolled back!'
    PRINT 'Error Number: ' + CAST(@ErrorNumber AS NVARCHAR(10))
    PRINT 'Error Line: ' + CAST(@ErrorLine AS NVARCHAR(10))
    PRINT 'Error Severity: ' + CAST(@ErrorSeverity AS NVARCHAR(10))
    PRINT 'Error State: ' + CAST(@ErrorState AS NVARCHAR(10))
    IF @ErrorProcedure IS NOT NULL
        PRINT 'Error Procedure: ' + @ErrorProcedure
    PRINT 'Error Message: ' + @ErrorMessage

    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
END CATCH
